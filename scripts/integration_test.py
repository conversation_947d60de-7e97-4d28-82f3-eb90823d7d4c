#!/usr/bin/env python3

import torch
import torch.nn as nn
import pickle
import sys
import os
import json
from typing import Dict, Any, Tuple

# Import both model architectures
sys.path.append('.')
from colab_model import LanguageModel as ColabLanguageModel
from src.model.transformer import Transformer as LocalTransformer
from scripts.test_colab_model import load_colab_model

class ModelIntegrationTester:
    def __init__(self):
        self.device = torch.device("mps" if torch.backends.mps.is_available() else 
                                 "cuda" if torch.cuda.is_available() else "cpu")
        
    def test_colab_model_loading(self) -> bool:
        """Test if Colab model loads correctly"""
        print("🔍 Testing Colab model loading...")
        
        try:
            model, tokenizer = load_colab_model()
            if model is None or tokenizer is None:
                print("❌ Failed to load Colab model")
                return False
            
            print(f"✅ Colab model loaded successfully")
            print(f"   - Model type: {type(model)}")
            print(f"   - Tokenizer type: {type(tokenizer)}")
            print(f"   - Model parameters: {sum(p.numel() for p in model.parameters()):,}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading Colab model: {e}")
            return False
    
    def test_model_architecture_compatibility(self) -> Dict[str, Any]:
        """Compare Colab and local model architectures"""
        print("\n🔍 Testing model architecture compatibility...")
        
        results = {
            'colab_model': None,
            'local_model': None,
            'compatibility': False,
            'differences': []
        }
        
        try:
            # Load Colab model
            colab_model, tokenizer = load_colab_model()
            if colab_model is None:
                results['differences'].append("Could not load Colab model")
                return results
            
            results['colab_model'] = {
                'type': str(type(colab_model)),
                'parameters': sum(p.numel() for p in colab_model.parameters()),
                'architecture': self._analyze_colab_architecture(colab_model)
            }
            
            # Create local model with similar config
            vocab_size = colab_model.head.out_features
            embed_dim = colab_model.embed_dim
            
            local_model = LocalTransformer(
                vocab_size=vocab_size,
                embed_dim=embed_dim,
                num_layers=2,  # Adjust based on colab model
                num_heads=4,   # Adjust based on colab model
                ff_dim=embed_dim * 4,
                max_seq_len=64,
                dropout=0.1
            ).to(self.device)
            
            results['local_model'] = {
                'type': str(type(local_model)),
                'parameters': sum(p.numel() for p in local_model.parameters()),
                'architecture': self._analyze_local_architecture(local_model)
            }
            
            # Compare architectures
            colab_arch = results['colab_model']['architecture']
            local_arch = results['local_model']['architecture']
            
            if colab_arch['vocab_size'] != local_arch['vocab_size']:
                results['differences'].append(f"Vocab size mismatch: {colab_arch['vocab_size']} vs {local_arch['vocab_size']}")
            
            if colab_arch['embed_dim'] != local_arch['embed_dim']:
                results['differences'].append(f"Embed dim mismatch: {colab_arch['embed_dim']} vs {local_arch['embed_dim']}")
            
            results['compatibility'] = len(results['differences']) == 0
            
            print(f"✅ Architecture analysis complete")
            print(f"   - Colab model: {results['colab_model']['parameters']:,} parameters")
            print(f"   - Local model: {results['local_model']['parameters']:,} parameters")
            print(f"   - Compatible: {results['compatibility']}")
            
            return results
            
        except Exception as e:
            print(f"❌ Error in architecture compatibility test: {e}")
            results['differences'].append(f"Error: {e}")
            return results
    
    def _analyze_colab_architecture(self, model: ColabLanguageModel) -> Dict[str, Any]:
        """Analyze Colab model architecture"""
        return {
            'vocab_size': model.head.out_features,
            'embed_dim': model.embed_dim,
            'num_layers': len(model.blocks),
            'num_heads': model.blocks[0].attention.num_heads if model.blocks else 0,
            'max_seq_len': model.pos_embedding.num_embeddings,
            'model_type': 'decoder_only'
        }
    
    def _analyze_local_architecture(self, model: LocalTransformer) -> Dict[str, Any]:
        """Analyze local model architecture"""
        return {
            'vocab_size': model.output_linear.out_features,
            'embed_dim': model.token_embedding.embed_dim,
            'num_layers': len(model.encoder_layers),
            'num_heads': model.encoder_layers[0].self_attention.num_heads if model.encoder_layers else 0,
            'max_seq_len': model.positional_encoding.max_seq_len,
            'model_type': 'encoder_decoder'
        }
    
    def test_checkpoint_loading(self) -> bool:
        """Test loading checkpoints with local trainer"""
        print("\n🔍 Testing checkpoint loading with local trainer...")
        
        try:
            # Import local trainer
            from src.training.trainer import Trainer
            
            # Check if we can load the Colab checkpoint format
            checkpoint_path = "checkpoints/phase1_english/colab_model.pt"
            if not os.path.exists(checkpoint_path):
                print("❌ Colab checkpoint not found")
                return False
            
            checkpoint = torch.load(checkpoint_path, map_location=self.device)
            
            print("✅ Checkpoint loaded successfully")
            print(f"   - Keys: {list(checkpoint.keys())}")
            print(f"   - Epoch: {checkpoint.get('epoch', 'N/A')}")
            print(f"   - Loss: {checkpoint.get('loss', 'N/A')}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading checkpoint: {e}")
            return False
    
    def test_tokenizer_compatibility(self) -> bool:
        """Test tokenizer compatibility"""
        print("\n🔍 Testing tokenizer compatibility...")
        
        try:
            # Load Colab tokenizer
            _, tokenizer = load_colab_model()
            if tokenizer is None:
                print("❌ Could not load tokenizer")
                return False
            
            # Test encoding/decoding
            test_text = "Hello world, this is a test."
            
            if hasattr(tokenizer, 'encode') and hasattr(tokenizer.encode(test_text), 'ids'):
                # HuggingFace tokenizer
                encoded = tokenizer.encode(test_text).ids
                decoded = tokenizer.decode(encoded)
            else:
                # Custom BPE tokenizer
                encoded = tokenizer.encode(test_text)
                decoded = tokenizer.decode(encoded)
            
            print("✅ Tokenizer compatibility test passed")
            print(f"   - Original: '{test_text}'")
            print(f"   - Encoded: {encoded[:10]}... (length: {len(encoded)})")
            print(f"   - Decoded: '{decoded}'")
            
            return True
            
        except Exception as e:
            print(f"❌ Error in tokenizer compatibility test: {e}")
            return False
    
    def create_adapter_for_local_training(self) -> bool:
        """Create an adapter to use Colab model with local training pipeline"""
        print("\n🔧 Creating adapter for local training...")
        
        try:
            # Load Colab model
            colab_model, tokenizer = load_colab_model()
            if colab_model is None:
                return False
            
            # Create adapter class
            adapter_code = '''
import torch
import torch.nn as nn
from colab_model import LanguageModel

class ColabModelAdapter(nn.Module):
    """Adapter to use Colab model with local training pipeline"""
    
    def __init__(self, colab_model_path: str, tokenizer_path: str):
        super().__init__()
        
        # Load the Colab model
        checkpoint = torch.load(colab_model_path, map_location='cpu')
        self.model = LanguageModel(**checkpoint['config'])
        self.model.load_state_dict(checkpoint['model_state_dict'])
        
        # Load tokenizer
        import pickle
        with open(tokenizer_path, 'rb') as f:
            self.tokenizer = pickle.load(f)
    
    def forward(self, input_ids, attention_mask=None, labels=None):
        """Forward pass compatible with local training pipeline"""
        logits, loss = self.model(input_ids, labels)
        
        return {
            'logits': logits,
            'loss': loss,
            'hidden_states': None,
            'attentions': None
        }
    
    def generate(self, input_ids, max_length=50, temperature=0.8, **kwargs):
        """Generation method compatible with local pipeline"""
        # Implementation would go here
        pass
'''
            
            # Save adapter
            adapter_path = "src/model/colab_adapter.py"
            with open(adapter_path, 'w') as f:
                f.write(adapter_code)
            
            print(f"✅ Adapter created at {adapter_path}")
            return True
            
        except Exception as e:
            print(f"❌ Error creating adapter: {e}")
            return False
    
    def run_comprehensive_integration_test(self) -> Dict[str, bool]:
        """Run all integration tests"""
        print("🚀 COMPREHENSIVE INTEGRATION TEST")
        print("=" * 60)
        
        results = {}
        
        # Test 1: Colab model loading
        results['colab_loading'] = self.test_colab_model_loading()
        
        # Test 2: Architecture compatibility
        arch_results = self.test_model_architecture_compatibility()
        results['architecture_compatibility'] = arch_results['compatibility']
        
        # Test 3: Checkpoint loading
        results['checkpoint_loading'] = self.test_checkpoint_loading()
        
        # Test 4: Tokenizer compatibility
        results['tokenizer_compatibility'] = self.test_tokenizer_compatibility()
        
        # Test 5: Create adapter
        results['adapter_creation'] = self.create_adapter_for_local_training()
        
        # Summary
        print(f"\n📊 INTEGRATION TEST RESULTS:")
        print("=" * 40)
        for test_name, passed in results.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"{test_name}: {status}")
        
        overall_success = all(results.values())
        print(f"\nOverall Integration: {'✅ SUCCESS' if overall_success else '❌ NEEDS WORK'}")
        
        return results

def main():
    tester = ModelIntegrationTester()
    results = tester.run_comprehensive_integration_test()
    
    if not all(results.values()):
        print("\n💡 RECOMMENDATIONS:")
        if not results.get('colab_loading', True):
            print("- Ensure Colab model files are properly downloaded and placed")
        if not results.get('architecture_compatibility', True):
            print("- Consider creating a unified model architecture")
        if not results.get('checkpoint_loading', True):
            print("- Update local trainer to handle Colab checkpoint format")
        if not results.get('tokenizer_compatibility', True):
            print("- Standardize tokenizer interface across the project")

if __name__ == "__main__":
    main()
