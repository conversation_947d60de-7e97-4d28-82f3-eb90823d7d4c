#!/usr/bin/env python3

import torch
import pickle
import sys
import os
import collections
import json
import re
from typing import List, Dict, <PERSON>ple
import numpy as np

# Import the model loading functions from test_colab_model
sys.path.append('.')
from scripts.test_colab_model import load_colab_model, generate_text

class ModelEvaluator:
    def __init__(self, model, tokenizer):
        self.model = model
        self.tokenizer = tokenizer
        self.device = next(model.parameters()).device
        
    def evaluate_text_quality(self, prompts: List[str], num_samples: int = 3) -> Dict:
        """Comprehensive evaluation of text generation quality"""
        results = {
            'coherence_scores': [],
            'repetition_analysis': [],
            'unk_token_frequency': [],
            'response_lengths': [],
            'generated_samples': {}
        }
        
        for prompt in prompts:
            prompt_results = {
                'samples': [],
                'coherence_scores': [],
                'repetition_scores': [],
                'unk_counts': []
            }
            
            for i in range(num_samples):
                # Generate with different temperatures for variety
                temp = 0.7 + (i * 0.1)
                generated = generate_text(self.model, self.tokenizer, prompt, 
                                        max_length=50, temperature=temp)
                
                prompt_results['samples'].append(generated)
                
                # Analyze coherence (simple heuristic)
                coherence = self._analyze_coherence(generated)
                prompt_results['coherence_scores'].append(coherence)
                
                # Analyze repetition
                repetition = self._analyze_repetition(generated)
                prompt_results['repetition_scores'].append(repetition)
                
                # Count UNK tokens
                unk_count = self._count_unk_tokens(generated)
                prompt_results['unk_counts'].append(unk_count)
                
                results['response_lengths'].append(len(generated.split()))
            
            results['generated_samples'][prompt] = prompt_results
            results['coherence_scores'].extend(prompt_results['coherence_scores'])
            results['repetition_analysis'].extend(prompt_results['repetition_scores'])
            results['unk_token_frequency'].extend(prompt_results['unk_counts'])
        
        return results
    
    def _analyze_coherence(self, text: str) -> float:
        """Simple coherence analysis based on sentence structure and flow"""
        sentences = text.split('.')
        if len(sentences) < 2:
            return 0.5  # Single sentence, neutral score
        
        coherence_score = 1.0
        
        # Check for abrupt topic changes (simple heuristic)
        words = text.lower().split()
        unique_words = set(words)
        
        # Penalize if too many unique words (might indicate incoherence)
        if len(unique_words) / len(words) > 0.8:
            coherence_score -= 0.3
        
        # Check for incomplete sentences
        if text.count('.') == 0 or text.endswith(' ,') or text.endswith(' and'):
            coherence_score -= 0.2
        
        # Check for grammatical patterns
        if ' the the ' in text or ' a a ' in text or ' and and ' in text:
            coherence_score -= 0.3
        
        return max(0.0, min(1.0, coherence_score))
    
    def _analyze_repetition(self, text: str) -> float:
        """Analyze repetitive patterns in generated text"""
        words = text.lower().split()
        if len(words) < 3:
            return 0.0
        
        # Check for immediate word repetition
        immediate_repetition = sum(1 for i in range(len(words)-1) 
                                 if words[i] == words[i+1]) / len(words)
        
        # Check for phrase repetition (2-3 word phrases)
        phrase_repetition = 0
        for phrase_len in [2, 3]:
            phrases = [' '.join(words[i:i+phrase_len]) 
                      for i in range(len(words)-phrase_len+1)]
            phrase_counts = collections.Counter(phrases)
            repeated_phrases = sum(count - 1 for count in phrase_counts.values() if count > 1)
            phrase_repetition += repeated_phrases / len(phrases)
        
        return immediate_repetition + phrase_repetition
    
    def _count_unk_tokens(self, text: str) -> int:
        """Count unknown tokens in generated text"""
        return text.count('<unk>') + text.count('[UNK]')
    
    def evaluate_prompt_diversity(self, base_prompt: str, num_generations: int = 10) -> Dict:
        """Evaluate diversity of generations from the same prompt"""
        generations = []
        for i in range(num_generations):
            temp = 0.8 + (i % 3) * 0.1  # Vary temperature
            generated = generate_text(self.model, self.tokenizer, base_prompt, 
                                    max_length=30, temperature=temp)
            generations.append(generated)
        
        # Calculate diversity metrics
        unique_generations = len(set(generations))
        diversity_ratio = unique_generations / num_generations
        
        # Calculate average pairwise similarity (simple word overlap)
        similarities = []
        for i in range(len(generations)):
            for j in range(i+1, len(generations)):
                sim = self._calculate_similarity(generations[i], generations[j])
                similarities.append(sim)
        
        avg_similarity = np.mean(similarities) if similarities else 0.0
        
        return {
            'total_generations': num_generations,
            'unique_generations': unique_generations,
            'diversity_ratio': diversity_ratio,
            'average_similarity': avg_similarity,
            'samples': generations[:5]  # Show first 5 samples
        }
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate simple word overlap similarity"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 and not words2:
            return 1.0
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)

def main():
    print("🔍 COMPREHENSIVE MODEL EVALUATION")
    print("=" * 60)
    
    # Load model
    model, tokenizer = load_colab_model()
    if model is None:
        return
    
    evaluator = ModelEvaluator(model, tokenizer)
    
    # Test prompts for evaluation
    test_prompts = [
        "The future of artificial intelligence",
        "Once upon a time in a distant galaxy",
        "The most important thing in life is",
        "Technology has changed the world by",
        "In the year 2050, humans will",
        "Climate change is a serious issue because",
        "The benefits of renewable energy include",
        "Education plays a crucial role in"
    ]
    
    print("\n📊 EVALUATING TEXT QUALITY...")
    quality_results = evaluator.evaluate_text_quality(test_prompts, num_samples=2)
    
    # Print quality analysis
    print(f"\n📈 QUALITY METRICS:")
    print(f"Average Coherence Score: {np.mean(quality_results['coherence_scores']):.3f}")
    print(f"Average Repetition Score: {np.mean(quality_results['repetition_analysis']):.3f}")
    print(f"Average UNK Token Count: {np.mean(quality_results['unk_token_frequency']):.1f}")
    print(f"Average Response Length: {np.mean(quality_results['response_lengths']):.1f} words")
    
    # Show sample generations with analysis
    print(f"\n📝 SAMPLE GENERATIONS WITH ANALYSIS:")
    for prompt, data in list(quality_results['generated_samples'].items())[:3]:
        print(f"\nPrompt: '{prompt}'")
        for i, sample in enumerate(data['samples']):
            print(f"  Sample {i+1}: {sample}")
            print(f"    Coherence: {data['coherence_scores'][i]:.3f}, "
                  f"Repetition: {data['repetition_scores'][i]:.3f}, "
                  f"UNK Count: {data['unk_counts'][i]}")
    
    print(f"\n🎲 EVALUATING GENERATION DIVERSITY...")
    diversity_results = evaluator.evaluate_prompt_diversity(
        "The future of technology will", num_generations=8
    )
    
    print(f"\n📊 DIVERSITY METRICS:")
    print(f"Unique Generations: {diversity_results['unique_generations']}/{diversity_results['total_generations']}")
    print(f"Diversity Ratio: {diversity_results['diversity_ratio']:.3f}")
    print(f"Average Similarity: {diversity_results['average_similarity']:.3f}")
    
    print(f"\n🎯 DIVERSITY SAMPLES:")
    for i, sample in enumerate(diversity_results['samples']):
        print(f"  {i+1}: {sample}")
    
    # Generate recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    
    avg_coherence = np.mean(quality_results['coherence_scores'])
    avg_repetition = np.mean(quality_results['repetition_analysis'])
    avg_unk = np.mean(quality_results['unk_token_frequency'])
    
    if avg_coherence < 0.6:
        print("❗ LOW COHERENCE: Consider fine-tuning with more structured training data")
    
    if avg_repetition > 0.3:
        print("❗ HIGH REPETITION: Implement repetition penalty in generation")
    
    if avg_unk > 2:
        print("❗ HIGH UNK TOKENS: Expand tokenizer vocabulary or retrain with more diverse data")
    
    if diversity_results['diversity_ratio'] < 0.5:
        print("❗ LOW DIVERSITY: Increase temperature or implement nucleus sampling")
    
    print(f"\n✅ Evaluation complete!")

if __name__ == "__main__":
    main()
