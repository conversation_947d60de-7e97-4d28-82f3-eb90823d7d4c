#!/usr/bin/env python3

import torch
import pickle
import sys
import os
import re
import collections
import numpy as np
from typing import List, Dict, Tuple, Set

# Import the model loading functions
sys.path.append('.')
from scripts.test_colab_model import load_colab_model, generate_text

class EnglishFluencyDiagnostics:
    def __init__(self, model, tokenizer):
        self.model = model
        self.tokenizer = tokenizer
        self.device = next(model.parameters()).device
        # Common English stop words
        self.stop_words = {
            'i', 'me', 'my', 'myself', 'we', 'our', 'ours', 'ourselves', 'you', 'your', 'yours',
            'yourself', 'yourselves', 'he', 'him', 'his', 'himself', 'she', 'her', 'hers',
            'herself', 'it', 'its', 'itself', 'they', 'them', 'their', 'theirs', 'themselves',
            'what', 'which', 'who', 'whom', 'this', 'that', 'these', 'those', 'am', 'is', 'are',
            'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'having', 'do', 'does',
            'did', 'doing', 'a', 'an', 'the', 'and', 'but', 'if', 'or', 'because', 'as', 'until',
            'while', 'of', 'at', 'by', 'for', 'with', 'through', 'during', 'before', 'after',
            'above', 'below', 'up', 'down', 'in', 'out', 'on', 'off', 'over', 'under', 'again',
            'further', 'then', 'once'
        }

    def simple_sent_tokenize(self, text: str) -> List[str]:
        """Simple sentence tokenization"""
        # Split on sentence endings, but be careful with abbreviations
        sentences = re.split(r'[.!?]+\s+', text)
        return [s.strip() for s in sentences if s.strip()]

    def simple_word_tokenize(self, text: str) -> List[str]:
        """Simple word tokenization"""
        # Split on whitespace and punctuation, keep only alphabetic words
        words = re.findall(r'\b[a-zA-Z]+\b', text.lower())
        return words

    def comprehensive_fluency_analysis(self, num_samples: int = 20) -> Dict:
        """Comprehensive analysis of English fluency problems"""
        print("🔍 COMPREHENSIVE ENGLISH FLUENCY ANALYSIS")
        print("=" * 60)
        
        # Test prompts covering different domains
        test_prompts = [
            "The weather today is",
            "I believe that education",
            "Technology has revolutionized",
            "In my opinion, the most important",
            "Scientists have discovered",
            "The economy is currently",
            "Climate change affects",
            "Artificial intelligence will",
            "People should always",
            "The future of humanity",
            "Art and culture represent",
            "Democracy requires",
            "Innovation drives",
            "Health and wellness",
            "Communication between people"
        ]
        
        all_generations = []
        for prompt in test_prompts:
            for temp in [0.7, 0.8, 0.9]:
                generated = generate_text(self.model, self.tokenizer, prompt, 
                                        max_length=40, temperature=temp)
                all_generations.append({
                    'prompt': prompt,
                    'temperature': temp,
                    'generated': generated
                })
        
        # Analyze all generations
        analysis_results = {
            'coherence_analysis': self._analyze_coherence_issues(all_generations),
            'repetition_analysis': self._analyze_repetition_patterns(all_generations),
            'grammar_analysis': self._analyze_grammar_issues(all_generations),
            'context_analysis': self._analyze_context_understanding(all_generations),
            'vocabulary_analysis': self._analyze_vocabulary_issues(all_generations),
            'sentence_structure_analysis': self._analyze_sentence_structure(all_generations)
        }
        
        return analysis_results
    
    def _analyze_coherence_issues(self, generations: List[Dict]) -> Dict:
        """Analyze coherence and logical flow issues"""
        print("\n📝 Analyzing coherence issues...")
        
        coherence_issues = {
            'topic_drift': 0,
            'incomplete_thoughts': 0,
            'logical_inconsistencies': 0,
            'abrupt_transitions': 0,
            'examples': []
        }
        
        for gen in generations:
            text = gen['generated']
            sentences = self.simple_sent_tokenize(text)

            # Check for topic drift
            if len(sentences) > 1:
                first_words = set(self.simple_word_tokenize(sentences[0]))
                last_words = set(self.simple_word_tokenize(sentences[-1]))
                overlap = len(first_words.intersection(last_words))
                if overlap < 2:  # Very little word overlap suggests topic drift
                    coherence_issues['topic_drift'] += 1
                    coherence_issues['examples'].append({
                        'issue': 'topic_drift',
                        'prompt': gen['prompt'],
                        'text': text,
                        'problem': 'Topic changes abruptly between sentences'
                    })
            
            # Check for incomplete thoughts
            if not text.strip().endswith(('.', '!', '?')):
                coherence_issues['incomplete_thoughts'] += 1
                coherence_issues['examples'].append({
                    'issue': 'incomplete_thoughts',
                    'prompt': gen['prompt'],
                    'text': text,
                    'problem': 'Text ends without completing the thought'
                })
            
            # Check for abrupt transitions
            transition_words = {'however', 'therefore', 'meanwhile', 'furthermore', 'moreover', 'consequently'}
            if any(word in text.lower() for word in transition_words):
                # Check if transition makes sense in context
                words = self.simple_word_tokenize(text)
                for i, word in enumerate(words):
                    if word in transition_words:
                        # Simple heuristic: check if there's logical connection
                        context_before = ' '.join(words[max(0, i-5):i])
                        context_after = ' '.join(words[i+1:i+6])
                        if len(context_before.split()) < 3 or len(context_after.split()) < 3:
                            coherence_issues['abrupt_transitions'] += 1
                            break
        
        return coherence_issues
    
    def _analyze_repetition_patterns(self, generations: List[Dict]) -> Dict:
        """Analyze repetitive patterns and phrases"""
        print("🔄 Analyzing repetition patterns...")
        
        repetition_analysis = {
            'word_repetition': {},
            'phrase_repetition': {},
            'pattern_repetition': {},
            'examples': []
        }
        
        all_texts = [gen['generated'] for gen in generations]
        
        # Analyze word repetition
        all_words = []
        for text in all_texts:
            words = self.simple_word_tokenize(text)
            all_words.extend(words)
        
        word_counts = collections.Counter(all_words)
        common_words = {word: count for word, count in word_counts.most_common(20) 
                       if word not in self.stop_words and count > 3}
        repetition_analysis['word_repetition'] = common_words
        
        # Analyze phrase repetition (2-3 word phrases)
        all_phrases = []
        for text in all_texts:
            words = self.simple_word_tokenize(text)
            for i in range(len(words) - 1):
                phrase = ' '.join(words[i:i+2])
                all_phrases.append(phrase)
        
        phrase_counts = collections.Counter(all_phrases)
        common_phrases = {phrase: count for phrase, count in phrase_counts.most_common(15) 
                         if count > 2}
        repetition_analysis['phrase_repetition'] = common_phrases
        
        # Find specific repetitive patterns
        for gen in generations:
            text = gen['generated']
            words = self.simple_word_tokenize(text)

            # Check for immediate repetition
            for i in range(len(words) - 1):
                if words[i] == words[i + 1]:
                    repetition_analysis['examples'].append({
                        'type': 'immediate_repetition',
                        'prompt': gen['prompt'],
                        'text': text,
                        'problem': f"Word '{words[i]}' repeated immediately"
                    })
                    break
        
        return repetition_analysis
    
    def _analyze_grammar_issues(self, generations: List[Dict]) -> Dict:
        """Analyze grammatical issues (simplified without POS tagging)"""
        print("📚 Analyzing grammar issues...")

        grammar_issues = {
            'double_articles': 0,
            'repeated_words': 0,
            'incomplete_sentences': 0,
            'examples': []
        }

        articles = {'a', 'an', 'the'}

        for gen in generations:
            text = gen['generated']
            sentences = self.simple_sent_tokenize(text)

            for sentence in sentences:
                words = self.simple_word_tokenize(sentence)

                # Check for double articles
                for i in range(len(words) - 1):
                    if words[i] in articles and words[i + 1] in articles:
                        grammar_issues['double_articles'] += 1
                        grammar_issues['examples'].append({
                            'type': 'double_articles',
                            'prompt': gen['prompt'],
                            'text': text,
                            'problem': f"Double article: {words[i]} {words[i+1]}"
                        })

                # Check for repeated words
                for i in range(len(words) - 1):
                    if words[i] == words[i + 1]:
                        grammar_issues['repeated_words'] += 1

                # Check for very short sentences (likely incomplete)
                if len(words) < 3:
                    grammar_issues['incomplete_sentences'] += 1

        return grammar_issues
    
    def _analyze_context_understanding(self, generations: List[Dict]) -> Dict:
        """Analyze context understanding issues"""
        print("🧠 Analyzing context understanding...")
        
        context_issues = {
            'prompt_relevance': 0,
            'semantic_coherence': 0,
            'factual_consistency': 0,
            'examples': []
        }
        
        for gen in generations:
            prompt = gen['prompt'].lower()
            text = gen['generated'].lower()
            
            # Check prompt relevance
            prompt_words = set(self.simple_word_tokenize(prompt))
            text_words = set(self.simple_word_tokenize(text))
            
            # Remove stop words for better analysis
            prompt_content = prompt_words - self.stop_words
            text_content = text_words - self.stop_words
            
            if prompt_content:
                relevance_score = len(prompt_content.intersection(text_content)) / len(prompt_content)
                if relevance_score < 0.3:  # Less than 30% overlap
                    context_issues['prompt_relevance'] += 1
                    context_issues['examples'].append({
                        'type': 'low_relevance',
                        'prompt': gen['prompt'],
                        'text': gen['generated'],
                        'problem': f"Generated text has low relevance to prompt (score: {relevance_score:.2f})"
                    })
        
        return context_issues
    
    def _analyze_vocabulary_issues(self, generations: List[Dict]) -> Dict:
        """Analyze vocabulary and word choice issues"""
        print("📖 Analyzing vocabulary issues...")
        
        vocab_issues = {
            'unusual_combinations': [],
            'archaic_words': [],
            'technical_jargon': [],
            'word_diversity': 0
        }
        
        all_words = []
        for gen in generations:
            words = self.simple_word_tokenize(gen['generated'])
            all_words.extend(words)
        
        # Calculate vocabulary diversity
        unique_words = len(set(all_words))
        total_words = len(all_words)
        vocab_issues['word_diversity'] = unique_words / total_words if total_words > 0 else 0
        
        # Look for unusual word combinations
        word_pairs = []
        for gen in generations:
            words = self.simple_word_tokenize(gen['generated'])
            for i in range(len(words) - 1):
                word_pairs.append((words[i], words[i + 1]))
        
        # Find uncommon combinations (simplified heuristic)
        pair_counts = collections.Counter(word_pairs)
        for (word1, word2), count in pair_counts.items():
            if count == 1 and len(word1) > 6 and len(word2) > 6:
                vocab_issues['unusual_combinations'].append(f"{word1} {word2}")

        return vocab_issues
    
    def _analyze_sentence_structure(self, generations: List[Dict]) -> Dict:
        """Analyze sentence structure issues"""
        print("🏗️ Analyzing sentence structure...")
        
        structure_issues = {
            'avg_sentence_length': 0,
            'sentence_variety': 0,
            'fragment_count': 0,
            'run_on_count': 0,
            'examples': []
        }
        
        all_sentences = []
        for gen in generations:
            sentences = self.simple_sent_tokenize(gen['generated'])
            all_sentences.extend(sentences)

        if all_sentences:
            # Calculate average sentence length
            sentence_lengths = [len(self.simple_word_tokenize(sent)) for sent in all_sentences]
            structure_issues['avg_sentence_length'] = np.mean(sentence_lengths)

            # Check for fragments and run-ons
            for i, sentence in enumerate(all_sentences):
                words = self.simple_word_tokenize(sentence)
                if len(words) < 4:  # Likely fragment
                    structure_issues['fragment_count'] += 1
                elif len(words) > 25:  # Likely run-on
                    structure_issues['run_on_count'] += 1

            # Calculate sentence variety (standard deviation of lengths)
            structure_issues['sentence_variety'] = np.std(sentence_lengths)
        
        return structure_issues
    
    def generate_diagnostic_report(self, analysis_results: Dict) -> str:
        """Generate a comprehensive diagnostic report"""
        report = []
        report.append("🏥 ENGLISH FLUENCY DIAGNOSTIC REPORT")
        report.append("=" * 60)
        
        # Coherence Issues
        coherence = analysis_results['coherence_analysis']
        report.append(f"\n📝 COHERENCE ISSUES:")
        report.append(f"   Topic Drift: {coherence['topic_drift']} instances")
        report.append(f"   Incomplete Thoughts: {coherence['incomplete_thoughts']} instances")
        report.append(f"   Abrupt Transitions: {coherence['abrupt_transitions']} instances")
        
        # Repetition Issues
        repetition = analysis_results['repetition_analysis']
        report.append(f"\n🔄 REPETITION ISSUES:")
        report.append(f"   Most Repeated Words: {list(repetition['word_repetition'].keys())[:5]}")
        report.append(f"   Most Repeated Phrases: {list(repetition['phrase_repetition'].keys())[:3]}")
        
        # Grammar Issues
        grammar = analysis_results['grammar_analysis']
        report.append(f"\n📚 GRAMMAR ISSUES:")
        report.append(f"   Double Articles: {grammar['double_articles']} instances")
        report.append(f"   Repeated Words: {grammar['repeated_words']} instances")
        report.append(f"   Incomplete Sentences: {grammar['incomplete_sentences']} instances")
        
        # Context Issues
        context = analysis_results['context_analysis']
        report.append(f"\n🧠 CONTEXT UNDERSTANDING:")
        report.append(f"   Low Prompt Relevance: {context['prompt_relevance']} instances")
        
        # Vocabulary Issues
        vocab = analysis_results['vocabulary_analysis']
        report.append(f"\n📖 VOCABULARY ANALYSIS:")
        report.append(f"   Word Diversity Score: {vocab['word_diversity']:.3f}")
        report.append(f"   Unusual Combinations: {len(vocab['unusual_combinations'])} found")
        
        # Sentence Structure
        structure = analysis_results['sentence_structure_analysis']
        report.append(f"\n🏗️ SENTENCE STRUCTURE:")
        report.append(f"   Average Sentence Length: {structure['avg_sentence_length']:.1f} words")
        report.append(f"   Sentence Fragments: {structure['fragment_count']} instances")
        report.append(f"   Run-on Sentences: {structure['run_on_count']} instances")
        
        # Priority Issues
        report.append(f"\n🚨 PRIORITY ISSUES TO ADDRESS:")
        if coherence['topic_drift'] > 5:
            report.append("   1. HIGH: Topic drift - model loses focus mid-generation")
        if repetition['word_repetition']:
            report.append("   2. HIGH: Word repetition - model gets stuck in loops")
        if context['prompt_relevance'] > 3:
            report.append("   3. MEDIUM: Poor prompt relevance - model ignores context")
        if structure['fragment_count'] > 5:
            report.append("   4. MEDIUM: Sentence fragments - incomplete thoughts")
        if vocab['word_diversity'] < 0.6:
            report.append("   5. LOW: Limited vocabulary diversity")
        
        return '\n'.join(report)

def main():
    print("🚀 Loading model for English fluency diagnostics...")
    
    model, tokenizer = load_colab_model()
    if model is None:
        return
    
    diagnostics = EnglishFluencyDiagnostics(model, tokenizer)
    
    # Run comprehensive analysis
    analysis_results = diagnostics.comprehensive_fluency_analysis()
    
    # Generate and display report
    report = diagnostics.generate_diagnostic_report(analysis_results)
    print(report)
    
    # Save detailed results
    import json
    with open('logs/fluency_diagnostics.json', 'w') as f:
        json.dump(analysis_results, f, indent=2, default=str)
    
    print(f"\n💾 Detailed analysis saved to logs/fluency_diagnostics.json")

if __name__ == "__main__":
    main()
