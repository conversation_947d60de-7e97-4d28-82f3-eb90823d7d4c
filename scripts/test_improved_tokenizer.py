#!/usr/bin/env python3
"""
Test script to validate the improved tokenizer fixes the issues.
"""

import sys
import os
sys.path.append('.')

from src.preprocessing.tokenizer import BPETokenizer

def test_improved_tokenizer():
    """Test the improved tokenizer with sample data."""
    print("🧪 Testing Improved BPE Tokenizer")
    print("=" * 50)
    
    # Create tokenizer
    tokenizer = BPETokenizer()
    
    # Test texts - including some that should be filtered
    test_texts = [
        "The quick brown fox jumps over the lazy dog.",
        "Artificial intelligence is transforming the world in remarkable ways.",
        "Once upon a time, in a distant galaxy far away, there lived a brave explorer.",
        "to help language model learn patterns from this diverse collection",  # Should be filtered
        "The weather today is beautiful and sunny.",
        "Technology has revolutionized communication and commerce.",
        "In the year 2050, humans will likely have advanced space travel.",
        "model will learn patterns from this diverse collection of text",  # Should be filtered
        "Science and mathematics are fundamental to understanding our universe.",
        "The future holds many exciting possibilities for humanity."
    ]
    
    print(f"Training tokenizer on {len(test_texts)} sample texts...")
    
    # Train with larger vocabulary
    tokenizer.train(test_texts, vocab_size=1000)
    
    print(f"\n📊 Tokenizer Statistics:")
    print(f"- Vocabulary size: {len(tokenizer.vocab)}")
    print(f"- Number of merges: {len(tokenizer.merges)}")
    print(f"- Special tokens: {tokenizer.special_tokens}")
    
    # Test encoding/decoding
    print(f"\n🔤 Testing Encoding/Decoding:")
    print("-" * 30)
    
    test_sentences = [
        "The future of artificial intelligence",
        "Once upon a time in a distant galaxy",
        "Technology has changed the world",
        "Hello world, this is a test"
    ]
    
    for sentence in test_sentences:
        encoded = tokenizer.encode(sentence)
        decoded = tokenizer.decode(encoded)
        
        print(f"Original: {sentence}")
        print(f"Encoded:  {encoded[:10]}{'...' if len(encoded) > 10 else ''} (len={len(encoded)})")
        print(f"Decoded:  {decoded}")
        print(f"Match:    {'✅' if sentence.lower().strip() == decoded.lower().strip() else '❌'}")
        print()
    
    # Test unknown token handling
    print(f"🔍 Testing Unknown Token Handling:")
    print("-" * 30)
    
    # Test with text containing characters not in training
    oov_text = "Żółć gęślą jaźń"  # Polish text with special characters
    encoded_oov = tokenizer.encode(oov_text)
    decoded_oov = tokenizer.decode(encoded_oov)
    
    print(f"OOV Text: {oov_text}")
    print(f"Encoded:  {encoded_oov}")
    print(f"Decoded:  {decoded_oov}")
    
    # Count unknown tokens
    unk_id = tokenizer.vocab.get(tokenizer.unk_token, -1)
    unk_count = encoded_oov.count(unk_id) if unk_id != -1 else 0
    print(f"Unknown tokens: {unk_count}/{len(encoded_oov)} ({unk_count/len(encoded_oov)*100:.1f}%)")
    
    # Test repetitive text filtering
    print(f"\n🚫 Testing Repetitive Text Filtering:")
    print("-" * 30)
    
    repetitive_texts = [
        "to help language model learn patterns from this diverse collection",
        "model will learn patterns from this text",
        "This is good text. This is good text. This is good text.",
        "Normal sentence without repetition."
    ]
    
    for text in repetitive_texts:
        is_repetitive = tokenizer.is_repetitive_text(text)
        print(f"Text: {text[:50]}{'...' if len(text) > 50 else ''}")
        print(f"Repetitive: {'🚫 YES' if is_repetitive else '✅ NO'}")
        print()
    
    print("✅ Tokenizer testing complete!")
    return tokenizer

def compare_with_old_issues():
    """Compare with the issues from the old model."""
    print("\n🔍 Comparing with Previous Issues:")
    print("=" * 50)
    
    tokenizer = test_improved_tokenizer()
    
    # Test the exact prompts that were problematic
    problematic_prompts = [
        "The future of artificial intelligence",
        "Once upon a time in a distant galaxy",
        "The most important thing in life is",
        "Technology has changed the world by",
        "In the year 2050, humans will"
    ]
    
    print("\n📝 Testing Problematic Prompts:")
    print("-" * 30)
    
    for prompt in problematic_prompts:
        encoded = tokenizer.encode(prompt)
        decoded = tokenizer.decode(encoded)
        
        # Check for issues
        has_unk = tokenizer.vocab[tokenizer.unk_token] in encoded
        has_repetitive = "patterns from this" in decoded.lower() or "diverse collection" in decoded.lower()
        
        print(f"Prompt: {prompt}")
        print(f"Encoded length: {len(encoded)}")
        print(f"Contains <unk>: {'❌ YES' if has_unk else '✅ NO'}")
        print(f"Has repetitive patterns: {'❌ YES' if has_repetitive else '✅ NO'}")
        print(f"Decoded: {decoded}")
        print()

if __name__ == "__main__":
    test_improved_tokenizer()
    compare_with_old_issues()
    
    print("\n🎉 All tests completed!")
    print("\nNext steps:")
    print("1. Copy the improved_colab_training.py to Google Colab")
    print("2. Run the training script in Colab")
    print("3. Download the improved model and tokenizer files")
    print("4. Test the improved model with scripts/test_colab_model.py")
