#!/usr/bin/env python3

import torch
import os
import sys
sys.path.append('.')

from src.model.transformer import Transformer
from src.preprocessing.tokenizer import BPETokenizer
from src.config.model_config import ModelConfig
from src.utils.checkpoint_manager import CheckpointManager

def generate_text(model, tokenizer, prompt, max_length=50, temperature=1.0, device='cpu'):
    """Generate text from a prompt using the trained model."""
    model.eval()
    
    # Encode the prompt
    input_ids = tokenizer.encode(prompt)
    input_tensor = torch.tensor([input_ids], dtype=torch.long).to(device)
    
    generated_ids = input_ids.copy()
    
    with torch.no_grad():
        for _ in range(max_length):
            # Create input tensor from current generated sequence
            current_input = torch.tensor([generated_ids], dtype=torch.long).to(device)
            
            # Forward pass - using the model as a decoder-only (src=tgt)
            outputs = model(current_input, current_input)
            
            # Get the logits for the last token
            next_token_logits = outputs[0, -1, :] / temperature
            
            # Sample from the distribution
            probs = torch.softmax(next_token_logits, dim=-1)
            next_token_id = torch.multinomial(probs, 1).item()
            
            # Add to generated sequence
            generated_ids.append(next_token_id)
            
            # Stop if we hit end of sequence or pad token
            if next_token_id == tokenizer.vocab.get('</w>', -1):
                break
    
    # Decode the generated sequence
    generated_text = tokenizer.decode(generated_ids)
    return generated_text

def main():
    # Setup device
    device = torch.device("mps" if torch.backends.mps.is_available() else 
                         "cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # Load tokenizer
    try:
        tokenizer = BPETokenizer.load_tokenizer(
            "data/vocab/tokenizer_model.json",
            "data/vocab/merges.json"
        )
        print(f"Loaded tokenizer with vocab size: {len(tokenizer.vocab)}")
    except FileNotFoundError:
        print("Tokenizer not found. Please run scripts/preprocess_data.py first.")
        return
    
    # Setup model config - match the training config
    model_config = ModelConfig(
        vocab_size=len(tokenizer.vocab),
        embed_dim=256,
        num_layers=2,
        num_heads=4,
        ff_dim=1024,
        max_seq_len=50,  # Match the checkpoint config
        dropout=0.1
    )
    
    # Initialize model
    model = Transformer(
        vocab_size=model_config.vocab_size,
        embed_dim=model_config.embed_dim,
        num_layers=model_config.num_layers,
        num_heads=model_config.num_heads,
        ff_dim=model_config.ff_dim,
        max_seq_len=model_config.max_seq_len,
        dropout=model_config.dropout
    ).to(device)
    
    # Load the latest checkpoint
    checkpoint_manager = CheckpointManager("checkpoints")
    checkpoint_path = "checkpoints/phase1_english/model_epoch_3.pt"
    
    if os.path.exists(checkpoint_path):
        checkpoint = checkpoint_manager.load_checkpoint(
            checkpoint_path, device, model, None, None
        )
        print(f"Loaded model from epoch {checkpoint['epoch']}")
        # Skip loss printing for now due to format issue
    else:
        print("No checkpoint found. Using randomly initialized model.")
    
    # Test prompts
    test_prompts = [
        "The quick brown fox",
        "Once upon a time",
        "In the beginning",
        "The weather today is",
        "I think that"
    ]
    
    print("\n" + "="*50)
    print("TESTING MODEL FLUENCY")
    print("="*50)
    
    for prompt in test_prompts:
        print(f"\nPrompt: '{prompt}'")
        print("-" * 30)
        
        # Generate with different temperatures
        for temp in [0.7, 1.0, 1.3]:
            generated = generate_text(model, tokenizer, prompt, 
                                    max_length=20, temperature=temp, device=device)
            print(f"Temp {temp}: {generated}")
    
    print("\n" + "="*50)
    print("INTERACTIVE MODE")
    print("Enter prompts (or 'quit' to exit):")
    print("="*50)
    
    while True:
        try:
            user_prompt = input("\nPrompt: ").strip()
            if user_prompt.lower() in ['quit', 'exit', 'q']:
                break
            if user_prompt:
                generated = generate_text(model, tokenizer, user_prompt, 
                                        max_length=30, temperature=1.0, device=device)
                print(f"Generated: {generated}")
        except KeyboardInterrupt:
            break
    
    print("\nGoodbye!")

if __name__ == "__main__":
    main()