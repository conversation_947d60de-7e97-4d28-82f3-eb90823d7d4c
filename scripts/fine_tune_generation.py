#!/usr/bin/env python3

import torch
import torch.nn as nn
import torch.optim as optim
import pickle
import sys
import os
import json
from typing import List, Dict, Tuple
import numpy as np

# Import the model loading functions
sys.path.append('.')
from scripts.test_colab_model import load_colab_model

class GenerationFineTuner:
    def __init__(self, model, tokenizer):
        self.model = model
        self.tokenizer = tokenizer
        self.device = next(model.parameters()).device
        
    def create_quality_training_data(self) -> List[str]:
        """Create high-quality training examples for fine-tuning"""
        quality_examples = [
            "The future of artificial intelligence lies in creating systems that can understand and reason about the world in ways that complement human intelligence.",
            "Once upon a time in a distant galaxy, explorers discovered a planet where the laws of physics worked differently than anywhere else in the universe.",
            "The most important thing in life is finding balance between pursuing your dreams and maintaining meaningful relationships with the people you care about.",
            "Technology has changed the world by connecting people across vast distances and providing access to information that was once available only to a few.",
            "Climate change represents one of the greatest challenges of our time, requiring global cooperation and innovative solutions to protect our planet.",
            "Education plays a crucial role in society by empowering individuals with knowledge and skills needed to contribute meaningfully to their communities.",
            "The benefits of renewable energy include reducing greenhouse gas emissions, creating sustainable jobs, and decreasing dependence on fossil fuels.",
            "Scientific research continues to unlock mysteries of the universe, from the smallest particles to the largest cosmic structures.",
            "Art and creativity serve as powerful forms of human expression, allowing us to communicate emotions and ideas across cultural boundaries.",
            "The development of medicine has dramatically improved human health and longevity through advances in treatment and prevention of diseases."
        ]
        return quality_examples
    
    def prepare_training_batch(self, texts: List[str], max_length: int = 64) -> Tuple[torch.Tensor, torch.Tensor]:
        """Prepare a batch of training data"""
        input_ids = []
        attention_masks = []
        
        for text in texts:
            # Encode text
            if hasattr(self.tokenizer, 'encode') and hasattr(self.tokenizer.encode(text), 'ids'):
                tokens = self.tokenizer.encode(text).ids
            else:
                tokens = self.tokenizer.encode(text)
            
            # Truncate or pad to max_length
            if len(tokens) > max_length:
                tokens = tokens[:max_length]
            else:
                # Pad with zeros (assuming 0 is pad token)
                tokens = tokens + [0] * (max_length - len(tokens))
            
            input_ids.append(tokens)
            
            # Create attention mask (1 for real tokens, 0 for padding)
            attention_mask = [1 if token != 0 else 0 for token in tokens]
            attention_masks.append(attention_mask)
        
        return (torch.tensor(input_ids, dtype=torch.long).to(self.device),
                torch.tensor(attention_masks, dtype=torch.long).to(self.device))
    
    def fine_tune_with_quality_data(self, num_epochs: int = 5, learning_rate: float = 1e-5):
        """Fine-tune the model with high-quality examples"""
        print("🔧 Starting fine-tuning with quality data...")
        
        # Get quality training data
        quality_texts = self.create_quality_training_data()
        
        # Prepare optimizer
        optimizer = optim.AdamW(self.model.parameters(), lr=learning_rate, weight_decay=0.01)
        
        # Training loop
        self.model.train()
        for epoch in range(num_epochs):
            total_loss = 0
            num_batches = 0
            
            # Process in small batches
            batch_size = 2
            for i in range(0, len(quality_texts), batch_size):
                batch_texts = quality_texts[i:i+batch_size]
                
                # Prepare batch
                input_ids, attention_masks = self.prepare_training_batch(batch_texts)
                
                # Forward pass
                optimizer.zero_grad()
                
                # Create targets (shifted input for language modeling)
                targets = input_ids.clone()
                targets[:, :-1] = input_ids[:, 1:]
                targets[:, -1] = -1  # Ignore last position
                
                # Apply attention mask to targets
                targets[attention_masks == 0] = -1
                
                logits, loss = self.model(input_ids, targets)
                
                # Backward pass
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                optimizer.step()
                
                total_loss += loss.item()
                num_batches += 1
            
            avg_loss = total_loss / num_batches if num_batches > 0 else 0
            print(f"Epoch {epoch+1}/{num_epochs}, Average Loss: {avg_loss:.4f}")
        
        print("✅ Fine-tuning completed!")
    
    def optimize_generation_parameters(self) -> Dict:
        """Find optimal generation parameters through grid search"""
        print("🔍 Optimizing generation parameters...")
        
        test_prompts = [
            "The future of technology",
            "In a world where",
            "The key to success"
        ]
        
        # Parameter grid
        temperatures = [0.7, 0.8, 0.9]
        top_p_values = [0.8, 0.9, 0.95]
        repetition_penalties = [1.0, 1.1, 1.2]
        
        best_params = None
        best_score = -float('inf')
        
        for temp in temperatures:
            for top_p in top_p_values:
                for rep_penalty in repetition_penalties:
                    score = self._evaluate_parameters(test_prompts, temp, top_p, rep_penalty)
                    
                    if score > best_score:
                        best_score = score
                        best_params = {
                            'temperature': temp,
                            'top_p': top_p,
                            'repetition_penalty': rep_penalty,
                            'score': score
                        }
        
        print(f"✅ Best parameters found: {best_params}")
        return best_params
    
    def _evaluate_parameters(self, prompts: List[str], temperature: float, 
                           top_p: float, repetition_penalty: float) -> float:
        """Evaluate a set of generation parameters"""
        self.model.eval()
        total_score = 0
        
        with torch.no_grad():
            for prompt in prompts:
                generated = self._generate_with_params(prompt, temperature, top_p, repetition_penalty)
                score = self._score_generation(generated)
                total_score += score
        
        return total_score / len(prompts)
    
    def _generate_with_params(self, prompt: str, temperature: float, 
                            top_p: float, repetition_penalty: float) -> str:
        """Generate text with specific parameters"""
        # Encode prompt
        if hasattr(self.tokenizer, 'encode') and hasattr(self.tokenizer.encode(prompt), 'ids'):
            tokens = self.tokenizer.encode(prompt).ids
        else:
            tokens = self.tokenizer.encode(prompt)
        
        input_ids = torch.tensor([tokens], dtype=torch.long).to(self.device)
        generated = tokens.copy()
        
        for _ in range(30):  # Shorter for evaluation
            logits, _ = self.model(input_ids)
            next_token_logits = logits[0, -1, :] / temperature
            
            # Apply repetition penalty
            if repetition_penalty != 1.0:
                for token in set(generated):
                    if next_token_logits[token] > 0:
                        next_token_logits[token] /= repetition_penalty
                    else:
                        next_token_logits[token] *= repetition_penalty
            
            # Nucleus sampling
            sorted_logits, sorted_indices = torch.sort(next_token_logits, descending=True)
            cumulative_probs = torch.cumsum(torch.softmax(sorted_logits, dim=-1), dim=-1)
            
            sorted_indices_to_remove = cumulative_probs > top_p
            sorted_indices_to_remove[1:] = sorted_indices_to_remove[:-1].clone()
            sorted_indices_to_remove[0] = 0
            
            indices_to_remove = sorted_indices[sorted_indices_to_remove]
            next_token_logits[indices_to_remove] = -float('inf')
            
            probs = torch.softmax(next_token_logits, dim=-1)
            next_token = torch.multinomial(probs, 1).item()
            
            generated.append(next_token)
            input_ids = torch.cat([input_ids, torch.tensor([[next_token]], device=self.device)], dim=1)
            
            if input_ids.size(1) > 63:
                input_ids = input_ids[:, -63:]
        
        # Decode
        if hasattr(self.tokenizer, 'decode') and not hasattr(self.tokenizer, 'inverse_vocab'):
            return self.tokenizer.decode(generated)
        else:
            return self.tokenizer.decode(generated)
    
    def _score_generation(self, text: str) -> float:
        """Score the quality of generated text"""
        words = text.split()
        if len(words) < 5:
            return 0.0
        
        score = 1.0
        
        # Penalize repetition
        word_counts = {}
        for word in words:
            word_counts[word] = word_counts.get(word, 0) + 1
        
        repetition_penalty = sum(max(0, count - 2) for count in word_counts.values()) / len(words)
        score -= repetition_penalty * 0.5
        
        # Reward diversity
        unique_ratio = len(set(words)) / len(words)
        score += unique_ratio * 0.3
        
        # Penalize incomplete sentences
        if not text.strip().endswith('.') and not text.strip().endswith('!') and not text.strip().endswith('?'):
            score -= 0.2
        
        return max(0.0, score)
    
    def save_improved_model(self, save_path: str):
        """Save the fine-tuned model"""
        checkpoint = {
            'model_state_dict': self.model.state_dict(),
            'config': {
                'vocab_size': self.model.head.out_features,
                'embed_dim': self.model.embed_dim,
                'num_layers': len(self.model.blocks),
                'num_heads': self.model.blocks[0].attention.num_heads,
                'ff_dim': self.model.blocks[0].feed_forward.linear1.out_features,
                'max_seq_len': self.model.pos_embedding.num_embeddings,
                'dropout': 0.1
            },
            'generation_params': self.optimize_generation_parameters()
        }
        
        torch.save(checkpoint, save_path)
        print(f"✅ Improved model saved to {save_path}")

def main():
    print("🚀 FINE-TUNING FOR IMPROVED GENERATION")
    print("=" * 60)
    
    # Load model
    model, tokenizer = load_colab_model()
    if model is None:
        return
    
    fine_tuner = GenerationFineTuner(model, tokenizer)
    
    # Fine-tune with quality data
    fine_tuner.fine_tune_with_quality_data(num_epochs=3, learning_rate=5e-6)
    
    # Optimize generation parameters
    best_params = fine_tuner.optimize_generation_parameters()
    
    # Save improved model
    os.makedirs("checkpoints/phase1_english", exist_ok=True)
    fine_tuner.save_improved_model("checkpoints/phase1_english/improved_model.pt")
    
    # Test improved generation
    print("\n🧪 TESTING IMPROVED GENERATION:")
    test_prompts = [
        "The future of artificial intelligence",
        "Once upon a time in a distant galaxy",
        "The most important thing in life is"
    ]
    
    for prompt in test_prompts:
        print(f"\n📝 Prompt: '{prompt}'")
        generated = fine_tuner._generate_with_params(
            prompt, 
            best_params['temperature'], 
            best_params['top_p'], 
            best_params['repetition_penalty']
        )
        print(f"🤖 Generated: {generated}")

if __name__ == "__main__":
    main()
