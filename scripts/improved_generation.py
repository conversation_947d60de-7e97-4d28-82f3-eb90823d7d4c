#!/usr/bin/env python3

import torch
import torch.nn.functional as F
import pickle
import sys
import os
import numpy as np
from typing import List, Optional, Tuple

# Import the model loading functions
sys.path.append('.')
from scripts.test_colab_model import load_colab_model

class ImprovedGenerator:
    def __init__(self, model, tokenizer):
        self.model = model
        self.tokenizer = tokenizer
        self.device = next(model.parameters()).device
        
    def generate_with_nucleus_sampling(self, prompt: str, max_length: int = 50, 
                                     temperature: float = 0.8, top_p: float = 0.9,
                                     repetition_penalty: float = 1.1) -> str:
        """Generate text using nucleus (top-p) sampling with repetition penalty"""
        self.model.eval()
        
        # Encode prompt
        if hasattr(self.tokenizer, 'encode') and hasattr(self.tokenizer.encode(prompt), 'ids'):
            tokens = self.tokenizer.encode(prompt).ids
        else:
            tokens = self.tokenizer.encode(prompt)
        
        input_ids = torch.tensor([tokens], dtype=torch.long).to(self.device)
        generated = tokens.copy()
        
        with torch.no_grad():
            for step in range(max_length):
                logits, _ = self.model(input_ids)
                next_token_logits = logits[0, -1, :] / temperature
                
                # Apply repetition penalty
                if repetition_penalty != 1.0:
                    next_token_logits = self._apply_repetition_penalty(
                        next_token_logits, generated, repetition_penalty
                    )
                
                # Nucleus sampling (top-p)
                next_token = self._nucleus_sampling(next_token_logits, top_p)
                
                generated.append(next_token)
                input_ids = torch.cat([input_ids, torch.tensor([[next_token]], device=self.device)], dim=1)
                
                # Keep context manageable
                if input_ids.size(1) > 63:
                    input_ids = input_ids[:, -63:]
        
        # Decode
        if hasattr(self.tokenizer, 'decode') and not hasattr(self.tokenizer, 'inverse_vocab'):
            return self.tokenizer.decode(generated)
        else:
            return self.tokenizer.decode(generated)
    
    def _apply_repetition_penalty(self, logits: torch.Tensor, generated_tokens: List[int], 
                                penalty: float) -> torch.Tensor:
        """Apply repetition penalty to reduce repetitive generation"""
        for token in set(generated_tokens):
            if logits[token] > 0:
                logits[token] /= penalty
            else:
                logits[token] *= penalty
        return logits
    
    def _nucleus_sampling(self, logits: torch.Tensor, top_p: float) -> int:
        """Nucleus (top-p) sampling"""
        sorted_logits, sorted_indices = torch.sort(logits, descending=True)
        cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)
        
        # Remove tokens with cumulative probability above the threshold
        sorted_indices_to_remove = cumulative_probs > top_p
        sorted_indices_to_remove[1:] = sorted_indices_to_remove[:-1].clone()
        sorted_indices_to_remove[0] = 0
        
        indices_to_remove = sorted_indices[sorted_indices_to_remove]
        logits[indices_to_remove] = -float('inf')
        
        probs = F.softmax(logits, dim=-1)
        return torch.multinomial(probs, 1).item()
    
    def generate_with_beam_search(self, prompt: str, max_length: int = 50, 
                                num_beams: int = 4, length_penalty: float = 1.0) -> str:
        """Generate text using beam search"""
        self.model.eval()
        
        # Encode prompt
        if hasattr(self.tokenizer, 'encode') and hasattr(self.tokenizer.encode(prompt), 'ids'):
            tokens = self.tokenizer.encode(prompt).ids
        else:
            tokens = self.tokenizer.encode(prompt)
        
        input_ids = torch.tensor([tokens], dtype=torch.long).to(self.device)
        
        # Initialize beams
        beams = [(input_ids, 0.0)]  # (sequence, score)
        
        with torch.no_grad():
            for step in range(max_length):
                new_beams = []
                
                for seq, score in beams:
                    if seq.size(1) > 63:
                        seq = seq[:, -63:]
                    
                    logits, _ = self.model(seq)
                    next_token_logits = logits[0, -1, :]
                    log_probs = F.log_softmax(next_token_logits, dim=-1)
                    
                    # Get top-k candidates
                    top_k_probs, top_k_indices = torch.topk(log_probs, num_beams * 2)
                    
                    for i in range(len(top_k_indices)):
                        token = top_k_indices[i].item()
                        token_score = top_k_probs[i].item()
                        
                        new_seq = torch.cat([seq, torch.tensor([[token]], device=self.device)], dim=1)
                        new_score = score + token_score
                        
                        # Apply length penalty
                        if length_penalty != 1.0:
                            length_norm = ((new_seq.size(1) - len(tokens)) ** length_penalty)
                            new_score = new_score / length_norm
                        
                        new_beams.append((new_seq, new_score))
                
                # Keep top beams
                beams = sorted(new_beams, key=lambda x: x[1], reverse=True)[:num_beams]
        
        # Return best sequence
        best_seq = beams[0][0][0].tolist()
        
        # Decode
        if hasattr(self.tokenizer, 'decode') and not hasattr(self.tokenizer, 'inverse_vocab'):
            return self.tokenizer.decode(best_seq)
        else:
            return self.tokenizer.decode(best_seq)
    
    def generate_with_contrastive_search(self, prompt: str, max_length: int = 50,
                                       top_k: int = 4, alpha: float = 0.6) -> str:
        """Generate text using contrastive search for more coherent output"""
        self.model.eval()
        
        # Encode prompt
        if hasattr(self.tokenizer, 'encode') and hasattr(self.tokenizer.encode(prompt), 'ids'):
            tokens = self.tokenizer.encode(prompt).ids
        else:
            tokens = self.tokenizer.encode(prompt)
        
        input_ids = torch.tensor([tokens], dtype=torch.long).to(self.device)
        generated = tokens.copy()
        
        with torch.no_grad():
            for step in range(max_length):
                logits, _ = self.model(input_ids)
                next_token_logits = logits[0, -1, :]
                
                # Get top-k candidates
                top_k_probs, top_k_indices = torch.topk(F.softmax(next_token_logits, dim=-1), top_k)
                
                if step == 0:
                    # For first token, just use probability
                    next_token = top_k_indices[0].item()
                else:
                    # Calculate contrastive scores
                    scores = []
                    for i in range(top_k):
                        token = top_k_indices[i].item()
                        prob = top_k_probs[i].item()
                        
                        # Calculate similarity penalty (simplified)
                        similarity_penalty = 0.0
                        if token in generated[-10:]:  # Check last 10 tokens
                            similarity_penalty = 0.5
                        
                        contrastive_score = alpha * np.log(prob) - (1 - alpha) * similarity_penalty
                        scores.append((contrastive_score, token))
                    
                    # Select token with highest contrastive score
                    next_token = max(scores)[1]
                
                generated.append(next_token)
                input_ids = torch.cat([input_ids, torch.tensor([[next_token]], device=self.device)], dim=1)
                
                # Keep context manageable
                if input_ids.size(1) > 63:
                    input_ids = input_ids[:, -63:]
        
        # Decode
        if hasattr(self.tokenizer, 'decode') and not hasattr(self.tokenizer, 'inverse_vocab'):
            return self.tokenizer.decode(generated)
        else:
            return self.tokenizer.decode(generated)

def main():
    print("🚀 IMPROVED TEXT GENERATION")
    print("=" * 60)
    
    # Load model
    model, tokenizer = load_colab_model()
    if model is None:
        return
    
    generator = ImprovedGenerator(model, tokenizer)
    
    test_prompts = [
        "The future of artificial intelligence",
        "Once upon a time in a distant galaxy",
        "The most important thing in life is",
        "Technology has changed the world by"
    ]
    
    for prompt in test_prompts:
        print(f"\n📝 Prompt: '{prompt}'")
        print("-" * 50)
        
        # Original generation
        print("🔸 Original (top-k):")
        original = generator.generate_with_nucleus_sampling(prompt, temperature=0.8, top_p=1.0)
        print(f"   {original}")
        
        # Nucleus sampling
        print("🔹 Nucleus sampling (top-p=0.9):")
        nucleus = generator.generate_with_nucleus_sampling(prompt, temperature=0.8, top_p=0.9, repetition_penalty=1.1)
        print(f"   {nucleus}")
        
        # Beam search
        print("🔸 Beam search:")
        beam = generator.generate_with_beam_search(prompt, num_beams=4, length_penalty=1.2)
        print(f"   {beam}")
        
        # Contrastive search
        print("🔹 Contrastive search:")
        contrastive = generator.generate_with_contrastive_search(prompt, top_k=4, alpha=0.6)
        print(f"   {contrastive}")

if __name__ == "__main__":
    main()
