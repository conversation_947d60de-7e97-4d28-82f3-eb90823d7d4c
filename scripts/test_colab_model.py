#!/usr/bin/env python3

import torch
import pickle
import sys
import os
import collections
import json

# BPE Tokenizer - Same as Colab version
class BPETokenizer:
    def __init__(self):
        self.vocab = {}
        self.merges = {}
        self.inverse_vocab = {}
        self.unk_token = '<unk>'
        self.pad_token = '<pad>'
        self.eos_token = '</w>'

    def get_pairs(self, tokens):
        pairs = collections.defaultdict(int)
        for i in range(len(tokens) - 1):
            pairs[(tokens[i], tokens[i+1])] += 1
        return pairs

    def encode(self, text):
        words = text.split()
        encoded = []
        
        for word in words:
            if not word:
                continue
            
            tokens = list(word) + [self.eos_token]
            
            while True:
                pairs = self.get_pairs(tokens)
                potential_merges = [(p, self.merges[p]) for p in pairs.keys() if p in self.merges]
                
                if not potential_merges:
                    break
                
                selected_pair, new_token = max(potential_merges, key=lambda x: len(x[1]))
                first, second = selected_pair
                
                i = 0
                new_tokens = []
                while i < len(tokens):
                    if i < len(tokens) - 1 and tokens[i] == first and tokens[i+1] == second:
                        new_tokens.append(new_token)
                        i += 2
                    else:
                        new_tokens.append(tokens[i])
                        i += 1
                tokens = new_tokens
            
            for token in tokens:
                encoded.append(self.vocab.get(token, self.vocab[self.unk_token]))
        
        return encoded

    def decode(self, token_ids):
        tokens = [self.inverse_vocab.get(tid, '') for tid in token_ids]
        text = ''.join(tokens).replace(self.eos_token, ' ').strip()
        return ' '.join(text.split())

def load_colab_model():
    """Load the model trained in Google Colab"""
    
    # Check if files exist
    model_path = "checkpoints/phase1_english/colab_model.pt"
    tokenizer_path = "data/vocab/colab_tokenizer.pkl"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found at {model_path}")
        print("Please download 'best_model.pt' from Colab and place it there")
        return None, None
    
    if not os.path.exists(tokenizer_path):
        print(f"❌ Tokenizer not found at {tokenizer_path}")
        print("Please download 'tokenizer.pkl' from Colab and place it there")
        return None, None
    
    # Load tokenizer
    with open(tokenizer_path, 'rb') as f:
        tokenizer = pickle.load(f)
    
    # Load model checkpoint
    device = torch.device("mps" if torch.backends.mps.is_available() else 
                         "cuda" if torch.cuda.is_available() else "cpu")
    
    checkpoint = torch.load(model_path, map_location=device)
    
    # Import the model architecture
    import sys
    sys.path.append('.')
    from colab_model import LanguageModel
    
    model = LanguageModel(**checkpoint['config']).to(device)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    print(f"✅ Loaded Colab model from epoch {checkpoint['epoch']+1}")
    print(f"📊 Final loss: {checkpoint['loss']:.4f}")
    print(f"🧠 Model has {sum(p.numel() for p in model.parameters()):,} parameters")
    
    return model, tokenizer

def generate_text(model, tokenizer, prompt, max_length=50, temperature=1.0):
    """Generate text using the Colab-trained model"""
    model.eval()
    device = next(model.parameters()).device

    # Encode prompt - handle both custom BPE and HuggingFace tokenizers
    if hasattr(tokenizer, 'encode') and hasattr(tokenizer.encode(prompt), 'ids'):
        # HuggingFace tokenizer
        tokens = tokenizer.encode(prompt).ids
    else:
        # Custom BPE tokenizer
        tokens = tokenizer.encode(prompt)

    input_ids = torch.tensor([tokens], dtype=torch.long).to(device)
    
    generated = tokens.copy()

    with torch.no_grad():
        for _ in range(max_length):
            logits, _ = model(input_ids)
            next_token_logits = logits[0, -1, :] / temperature

            # Top-k sampling
            top_k = 50
            if top_k > 0:
                indices_to_remove = next_token_logits < torch.topk(next_token_logits, top_k)[0][..., -1, None]
                next_token_logits[indices_to_remove] = -float('inf')

            probs = torch.softmax(next_token_logits, dim=-1)
            next_token = torch.multinomial(probs, 1).item()

            generated.append(next_token)
            input_ids = torch.cat([input_ids, torch.tensor([[next_token]], device=device)], dim=1)

            # Keep context manageable
            if input_ids.size(1) > 63:
                input_ids = input_ids[:, -63:]

    # Decode - handle both tokenizer types
    if hasattr(tokenizer, 'decode') and not hasattr(tokenizer, 'inverse_vocab'):
        # HuggingFace tokenizer
        return tokenizer.decode(generated)
    else:
        # Custom BPE tokenizer
        return tokenizer.decode(generated)

def main():
    print("🚀 Loading your Colab-trained model...")
    
    model, tokenizer = load_colab_model()
    if model is None:
        return
    
    # Test prompts
    test_prompts = [
        "The future of artificial intelligence",
        "Once upon a time in a distant galaxy",
        "The most important thing in life is",
        "Technology has changed the world by",
        "In the year 2050, humans will"
    ]
    
    print("\n" + "="*60)
    print("🤖 TESTING YOUR COLAB MODEL")
    print("="*60)
    
    for prompt in test_prompts:
        print(f"\n📝 Prompt: '{prompt}'")
        print("-" * 40)
        
        generated = generate_text(model, tokenizer, prompt, max_length=40, temperature=0.8)
        print(f"🤖 Generated: {generated}")
    
    # Interactive mode
    print("\n" + "="*60)
    print("💬 INTERACTIVE MODE")
    print("Enter prompts (or 'quit' to exit)")
    print("="*60)
    
    while True:
        try:
            user_prompt = input("\n💭 Your prompt: ").strip()
            
            if user_prompt.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
                
            if user_prompt:
                generated = generate_text(model, tokenizer, user_prompt, max_length=50, temperature=0.9)
                print(f"🤖 Generated: {generated}")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break

if __name__ == "__main__":
    main()