import collections
import re
import json
import pickle
from typing import List, Dict, Tuple, Set

class ImprovedBPETokenizer:
    """
    Improved BPE tokenizer designed for better English text generation.
    Addresses issues with vocabulary size, unknown tokens, and text quality.
    """
    
    def __init__(self):
        self.vocab = {}
        self.merges = {}
        self.inverse_vocab = {}
        
        # Special tokens for better language modeling
        self.pad_token = '<pad>'
        self.unk_token = '<unk>'
        self.bos_token = '<bos>'  # Beginning of sequence
        self.eos_token = '<eos>'  # End of sequence
        self.special_tokens = [self.pad_token, self.unk_token, self.bos_token, self.eos_token]
        
        # Pre-tokenization pattern for better word boundary handling
        self.pre_tokenize_pattern = re.compile(r"'s|'t|'re|'ve|'m|'ll|'d| ?\p{L}+| ?\p{N}+| ?[^\s\p{L}\p{N}]+|\s+(?!\S)|\s+", re.UNICODE)
        
    def clean_text(self, text: str) -> str:
        """Clean and normalize text for better tokenization."""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        # Remove control characters
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        # Normalize quotes
        text = text.replace('"', '"').replace('"', '"')
        text = text.replace(''', "'").replace(''', "'")
        return text.strip()
    
    def pre_tokenize(self, text: str) -> List[str]:
        """Pre-tokenize text into words and subwords."""
        text = self.clean_text(text)
        # Simple word-level tokenization for better BPE training
        words = []
        for word in text.split():
            if word.strip():
                # Add end-of-word marker
                words.append(word + '</w>')
        return words
    
    def get_pairs(self, word_tokens: List[str]) -> Dict[Tuple[str, str], int]:
        """Get all adjacent pairs in word tokens with frequencies."""
        pairs = collections.defaultdict(int)
        for i in range(len(word_tokens) - 1):
            pairs[(word_tokens[i], word_tokens[i + 1])] += 1
        return pairs
    
    def train(self, texts: List[str], vocab_size: int = 8000):
        """
        Train the BPE tokenizer with improved vocabulary building.
        Uses larger vocabulary size and better text preprocessing.
        """
        print(f"Training improved tokenizer with vocab_size={vocab_size}")
        
        # Clean and filter texts
        cleaned_texts = []
        for text in texts:
            cleaned = self.clean_text(text)
            # Filter out very short or repetitive texts
            if len(cleaned) > 10 and not self._is_repetitive(cleaned):
                cleaned_texts.append(cleaned)
        
        print(f"Cleaned {len(cleaned_texts)} texts from {len(texts)} original texts")
        
        # Initialize vocabulary with special tokens
        self.vocab = {token: i for i, token in enumerate(self.special_tokens)}
        self.inverse_vocab = {i: token for i, token in enumerate(self.special_tokens)}
        current_id = len(self.special_tokens)
        
        # Build character vocabulary from cleaned texts
        all_chars = set()
        for text in cleaned_texts:
            for char in text:
                all_chars.add(char)
        
        # Add end-of-word marker
        all_chars.add('</w>')
        
        # Add characters to vocabulary
        for char in sorted(all_chars):
            if char not in self.vocab:
                self.vocab[char] = current_id
                self.inverse_vocab[current_id] = char
                current_id += 1
        
        # Prepare word frequency dictionary
        word_freq = collections.defaultdict(int)
        for text in cleaned_texts:
            words = self.pre_tokenize(text)
            for word in words:
                # Convert word to character list
                char_word = list(word)
                word_freq[tuple(char_word)] += 1
        
        print(f"Built initial vocabulary with {len(self.vocab)} tokens")
        print(f"Processing {len(word_freq)} unique words")
        
        # BPE training loop
        while len(self.vocab) < vocab_size:
            # Count all pairs across all words
            pairs_count = collections.defaultdict(int)
            
            for word_chars, freq in word_freq.items():
                pairs = self.get_pairs(list(word_chars))
                for pair, count in pairs.items():
                    pairs_count[pair] += count * freq
            
            if not pairs_count:
                break
            
            # Find most frequent pair
            best_pair = max(pairs_count.items(), key=lambda x: x[1])[0]
            
            # Create new merged token
            new_token = best_pair[0] + best_pair[1]
            
            # Add to vocabulary if not already present
            if new_token not in self.vocab:
                self.vocab[new_token] = current_id
                self.inverse_vocab[current_id] = new_token
                current_id += 1
            
            # Store merge rule
            self.merges[best_pair] = new_token
            
            # Update word frequency dictionary with merged tokens
            new_word_freq = collections.defaultdict(int)
            for word_chars, freq in word_freq.items():
                new_word = self._apply_merge(list(word_chars), best_pair, new_token)
                new_word_freq[tuple(new_word)] += freq
            
            word_freq = new_word_freq
            
            if len(self.vocab) % 500 == 0:
                print(f"Vocabulary size: {len(self.vocab)}")
        
        print(f"Training complete! Final vocabulary size: {len(self.vocab)}")
        print(f"Number of merge rules: {len(self.merges)}")
    
    def _is_repetitive(self, text: str) -> bool:
        """Check if text contains repetitive patterns that should be filtered."""
        # Check for common repetitive patterns
        repetitive_patterns = [
            "to help language model learn patterns",
            "diverse collection",
            "model will learn",
            "patterns from this",
            "language model",
            "help train our"
        ]
        
        text_lower = text.lower()
        for pattern in repetitive_patterns:
            if pattern in text_lower:
                return True
        
        # Check for excessive repetition of short phrases
        words = text.split()
        if len(words) > 5:
            for i in range(len(words) - 2):
                phrase = ' '.join(words[i:i+3])
                if text.count(phrase) > 2:
                    return True
        
        return False
    
    def _apply_merge(self, word_chars: List[str], pair: Tuple[str, str], new_token: str) -> List[str]:
        """Apply a merge rule to a word."""
        new_word = []
        i = 0
        while i < len(word_chars):
            if (i < len(word_chars) - 1 and 
                word_chars[i] == pair[0] and 
                word_chars[i + 1] == pair[1]):
                new_word.append(new_token)
                i += 2
            else:
                new_word.append(word_chars[i])
                i += 1
        return new_word
    
    def encode(self, text: str) -> List[int]:
        """Encode text to token IDs with improved handling."""
        if not text.strip():
            return []
        
        text = self.clean_text(text)
        words = self.pre_tokenize(text)
        
        encoded = []
        for word in words:
            # Convert to character tokens
            word_tokens = list(word)
            
            # Apply all learned merges
            while True:
                pairs = self.get_pairs(word_tokens)
                if not pairs:
                    break
                
                # Find the best merge to apply (prioritize longer merges)
                best_pair = None
                for pair in pairs:
                    if pair in self.merges:
                        if best_pair is None or len(self.merges[pair]) > len(self.merges[best_pair]):
                            best_pair = pair
                
                if best_pair is None:
                    break
                
                # Apply the merge
                word_tokens = self._apply_merge(word_tokens, best_pair, self.merges[best_pair])
            
            # Convert tokens to IDs
            for token in word_tokens:
                token_id = self.vocab.get(token, self.vocab[self.unk_token])
                encoded.append(token_id)
        
        return encoded
    
    def decode(self, token_ids: List[int]) -> str:
        """Decode token IDs back to text with improved handling."""
        if not token_ids:
            return ""
        
        tokens = []
        for token_id in token_ids:
            if token_id in self.inverse_vocab:
                token = self.inverse_vocab[token_id]
                # Skip special tokens in output
                if token not in [self.pad_token, self.bos_token, self.eos_token]:
                    tokens.append(token)
        
        # Join tokens and clean up
        text = ''.join(tokens)
        # Replace end-of-word markers with spaces
        text = text.replace('</w>', ' ')
        # Clean up extra spaces
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def save(self, filepath: str):
        """Save tokenizer to file."""
        tokenizer_data = {
            'vocab': self.vocab,
            'merges': self.merges,
            'inverse_vocab': self.inverse_vocab,
            'special_tokens': self.special_tokens
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(tokenizer_data, f)
        print(f"Tokenizer saved to {filepath}")
    
    def load(self, filepath: str):
        """Load tokenizer from file."""
        with open(filepath, 'rb') as f:
            tokenizer_data = pickle.load(f)
        
        self.vocab = tokenizer_data['vocab']
        self.merges = tokenizer_data['merges']
        self.inverse_vocab = tokenizer_data['inverse_vocab']
        self.special_tokens = tokenizer_data['special_tokens']
        print(f"Tokenizer loaded from {filepath}")
