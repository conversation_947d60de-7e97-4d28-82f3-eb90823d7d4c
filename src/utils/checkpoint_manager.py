import torch
import os

class CheckpointManager:
    """
    Manages saving and loading model checkpoints.
    """
    def __init__(self, checkpoint_dir: str = "checkpoints"):
        self.checkpoint_dir = checkpoint_dir
        os.makedirs(self.checkpoint_dir, exist_ok=True)

    def save_checkpoint(self, model: torch.nn.Module, optimizer: torch.optim.Optimizer, 
                        scheduler: torch.optim.lr_scheduler._LRScheduler, 
                        epoch: int, loss: float, vocab_size: int, phase: str = "phase1_english"):
        """
        Saves the current state of the model, optimizer, and scheduler.
        """
        phase_checkpoint_dir = os.path.join(self.checkpoint_dir, phase)
        os.makedirs(phase_checkpoint_dir, exist_ok=True)
        path = os.path.join(phase_checkpoint_dir, f"model_epoch_{epoch+1}.pt")

        checkpoint = {
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'loss': loss, 
            'vocab_size': vocab_size
        }
        torch.save(checkpoint, path)
        print(f"Checkpoint saved to {path} for epoch {epoch+1}")
        return path

    def load_checkpoint(self, path: str, device: torch.device, 
                        model: torch.nn.Module = None, optimizer: torch.optim.Optimizer = None, 
                        scheduler: torch.optim.lr_scheduler._LRScheduler = None):
        """
        Loads a checkpoint from the specified path.
        """
        if not os.path.exists(path):
            raise FileNotFoundError(f"Checkpoint file not found at: {path}")
        
        print(f"Loading checkpoint from {path}...")
        checkpoint = torch.load(path, map_location=device)
        
        if model:
            model.load_state_dict(checkpoint['model_state_dict'])
        if optimizer:
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        if scheduler:
            scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            
        print(f"Checkpoint loaded successfully. Resuming from epoch {checkpoint['epoch']+1}")
        return checkpoint

if __name__ == "__main__":
    # Example usage for testing
    import torch.nn as nn
    import torch.optim as optim
    from src.training.optimizers import get_optimizer # Re-using get_optimizer for test setup
    from src.training.schedulers import get_scheduler # Re-using get_scheduler for test setup

    # Dummy Model, Optimizer, Scheduler for testing
    class DummyModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.linear = nn.Linear(10, 1)

        def forward(self, x):
            return self.linear(x)

    model = DummyModel()
    optimizer = get_optimizer(model.parameters(), learning_rate=0.01)
    scheduler = get_scheduler(optimizer)

    device = torch.device("mps" if torch.backends.mps.is_available() else "cpu")
    model.to(device)

    # Initialize CheckpointManager
    checkpoint_manager = CheckpointManager(checkpoint_dir="test_checkpoints")

    # Test saving a checkpoint
    test_epoch = 0
    test_loss = 5.0
    test_vocab_size = 100
    saved_path = checkpoint_manager.save_checkpoint(model, optimizer, scheduler, 
                                                    test_epoch, test_loss, test_vocab_size,
                                                    phase="test_phase")
    print(f"Saved checkpoint to: {saved_path}")

    # Test loading a checkpoint
    # Create new instances to simulate loading into a fresh state
    new_model = DummyModel()
    new_optimizer = get_optimizer(new_model.parameters(), learning_rate=0.01)
    new_scheduler = get_scheduler(new_optimizer)

    loaded_checkpoint = checkpoint_manager.load_checkpoint(saved_path, device, new_model, new_optimizer, new_scheduler)
    print(f"Loaded epoch: {loaded_checkpoint['epoch']}")
    print(f"Loaded loss: {loaded_checkpoint['loss']}")
    print(f"Loaded vocab_size: {loaded_checkpoint['vocab_size']}")

    # Verify model states are the same (simple check)
    for p1, p2 in zip(model.parameters(), new_model.parameters()):
        assert torch.equal(p1, p2), "Model parameters mismatch after loading!"
    print("Model parameters match after loading.")

    # Test loading non-existent checkpoint
    try:
        checkpoint_manager.load_checkpoint("non_existent_path.pt", device)
    except FileNotFoundError as e:
        print(f"Caught expected error: {e}")

    # Clean up test files
    import shutil
    shutil.rmtree("test_checkpoints")
    print("Cleaned up test_checkpoints directory.") 