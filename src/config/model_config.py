class ModelConfig:
    def __init__(self,
                 vocab_size: int = 1000, # This will be updated dynamically from tokenizer
                 embed_dim: int = 256,
                 num_layers: int = 2,
                 num_heads: int = 4,
                 ff_dim: int = 1024,
                 max_seq_len: int = 1024, # Increased max_seq_len to handle longer sequences
                 dropout: float = 0.1):
        self.vocab_size = vocab_size
        self.embed_dim = embed_dim
        self.num_layers = num_layers
        self.num_heads = num_heads
        self.ff_dim = ff_dim
        self.max_seq_len = max_seq_len
        self.dropout = dropout

    def __repr__(self):
        return (f"ModelConfig(\n"
                f"    vocab_size={self.vocab_size},\n"
                f"    embed_dim={self.embed_dim},\n"
                f"    num_layers={self.num_layers},\n"
                f"    num_heads={self.num_heads},\n"
                f"    ff_dim={self.ff_dim},\n"
                f"    max_seq_len={self.max_seq_len},\n"
                f"    dropout={self.dropout}\n)")


class TrainingConfig:
    def __init__(self,
                 batch_size: int = 32,
                 learning_rate: float = 1e-4,
                 num_epochs: int = 10,
                 log_interval: int = 100,
                 checkpoint_interval: int = 500,
                 gradient_accumulation_steps: int = 1):
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        self.num_epochs = num_epochs
        self.log_interval = log_interval
        self.checkpoint_interval = checkpoint_interval
        self.gradient_accumulation_steps = gradient_accumulation_steps

    def __repr__(self):
        return (f"TrainingConfig(\n"
                f"    batch_size={self.batch_size},\n"
                f"    learning_rate={self.learning_rate},\n"
                f"    num_epochs={self.num_epochs},\n"
                f"    log_interval={self.log_interval},\n"
                f"    checkpoint_interval={self.checkpoint_interval},\n"
                f"    gradient_accumulation_steps={self.gradient_accumulation_steps}\n)")


class DataConfig:
    def __init__(self, 
                 raw_data_dir: str = "data/raw/english_corpus",
                 processed_data_dir: str = "data/processed/english_tokens",
                 vocab_dir: str = "data/vocab"): 
        self.raw_data_dir = raw_data_dir
        self.processed_data_dir = processed_data_dir
        self.vocab_dir = vocab_dir

    def __repr__(self):
        return (f"DataConfig(\n"
                f"    raw_data_dir='{self.raw_data_dir}',\n"
                f"    processed_data_dir='{self.processed_data_dir}',\n"
                f"    vocab_dir='{self.vocab_dir}'\n)")


if __name__ == "__main__":
    model_cfg = ModelConfig()
    train_cfg = TrainingConfig()
    data_cfg = DataConfig()

    print("\nModel Configuration:")
    print(model_cfg)

    print("\nTraining Configuration:")
    print(train_cfg)

    print("\nData Configuration:")
    print(data_cfg) 