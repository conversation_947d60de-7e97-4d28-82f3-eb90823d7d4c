import torch.optim as optim
from torch.optim.lr_scheduler import _LRScheduler

def get_scheduler(optimizer: optim.Optimizer, scheduler_type: str = "StepLR", **kwargs) -> _LRScheduler:
    """
    Returns a learning rate scheduler for the given optimizer.

    Args:
        optimizer: The optimizer for which to create the scheduler.
        scheduler_type: The type of scheduler (e.g., "StepLR", "ExponentialLR").
        **kwargs: Additional keyword arguments specific to the scheduler type.

    Returns:
        A PyTorch learning rate scheduler instance.
    """
    if scheduler_type == "StepLR":
        step_size = kwargs.get("step_size", 10)
        gamma = kwargs.get("gamma", 0.1)
        scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=step_size, gamma=gamma)
    elif scheduler_type == "ExponentialLR":
        gamma = kwargs.get("gamma", 0.95)
        scheduler = optim.lr_scheduler.ExponentialLR(optimizer, gamma=gamma)
    else:
        raise ValueError(f"Unsupported scheduler type: {scheduler_type}")
    
    return scheduler

if __name__ == "__main__":
    # Example usage:
    import torch.nn as nn
    from src.training.optimizers import get_optimizer

    class DummyModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.linear = nn.Linear(10, 1)

        def forward(self, x):
            return self.linear(x)

    model = DummyModel()
    optimizer = get_optimizer(model.parameters(), learning_rate=0.01, optimizer_type="Adam")

    # Test StepLR
    scheduler_steplr = get_scheduler(optimizer, "StepLR", step_size=5, gamma=0.5)
    print(f"StepLR Scheduler: {scheduler_steplr}")
    print(f"Initial LR: {scheduler_steplr.get_last_lr()[0]}")
    for epoch in range(10):
        optimizer.step() # Simulate optimizer step
        scheduler_steplr.step() # Update LR
        # print(f"Epoch {epoch+1}, LR: {scheduler_steplr.get_last_lr()[0]}")
    print(f"Final LR after 10 epochs: {scheduler_steplr.get_last_lr()[0]}")

    # Re-initialize optimizer for next test
    optimizer = get_optimizer(model.parameters(), learning_rate=0.01, optimizer_type="Adam")
    # Test ExponentialLR
    scheduler_exp = get_scheduler(optimizer, "ExponentialLR", gamma=0.9)
    print(f"\nExponentialLR Scheduler: {scheduler_exp}")
    print(f"Initial LR: {scheduler_exp.get_last_lr()[0]}")
    for epoch in range(10):
        optimizer.step()
        scheduler_exp.step()
        # print(f"Epoch {epoch+1}, LR: {scheduler_exp.get_last_lr()[0]}")
    print(f"Final LR after 10 epochs: {scheduler_exp.get_last_lr()[0]}")

    # Test unsupported scheduler
    try:
        get_scheduler(optimizer, "UnsupportedScheduler")
    except ValueError as e:
        print(f"Caught expected error: {e}")

    print("Schedulers tests passed.") 