{"cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# AI Language Model Phase 1 Training - Google Colab\n", "\n", "This notebook trains our custom transformer model from scratch on English text.\n", "Just run all cells and watch it learn!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup"}, "outputs": [], "source": ["# Install dependencies\n", "!pip install datasets torch transformers\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch.utils.data import DataLoader, Dataset\n", "import collections\n", "import json\n", "import os\n", "import time\n", "import math\n", "from datasets import load_dataset\n", "import numpy as np\n", "\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "if torch.cuda.is_available():\n", "    print(f\"GPU: {torch.cuda.get_device_name(0)}\")\n", "    device = torch.device('cuda')\nelse:\n", "    device = torch.device('cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "tokenizer"}, "outputs": [], "source": ["# BPE Tokenizer - Custom Implementation\n", "class BPETokenizer:\n", "    def __init__(self):\n", "        self.vocab = {}\n", "        self.merges = {}\n", "        self.inverse_vocab = {}\n", "        self.unk_token = '<unk>'\n", "        self.pad_token = '<pad>'\n", "        self.eos_token = '</w>'\n", "\n", "    def get_pairs(self, tokens):\n", "        pairs = collections.defaultdict(int)\n", "        for i in range(len(tokens) - 1):\n", "            pairs[(tokens[i], tokens[i+1])] += 1\n", "        return pairs\n", "\n", "    def train(self, texts, vocab_size):\n", "        # Initialize with characters\n", "        all_chars = set()\n", "        for text in texts:\n", "            for char in text:\n", "                all_chars.add(char)\n", "        \n", "        all_chars.update([self.eos_token, self.unk_token, self.pad_token])\n", "        sorted_chars = sorted(list(all_chars))\n", "        \n", "        self.vocab = {token: i for i, token in enumerate(sorted_chars)}\n", "        self.inverse_vocab = {i: token for i, token in enumerate(sorted_chars)}\n", "        current_id = len(sorted_chars)\n", "\n", "        # Prepare word segments\n", "        word_freq = collections.defaultdict(int)\n", "        for text in texts:\n", "            for word in text.split():\n", "                if word:\n", "                    char_segment = ' '.join(list(word)) + f' {self.eos_token}'\n", "                    word_freq[char_segment] += 1\n", "\n", "        current_segments = dict(word_freq)\n", "\n", "        while len(self.vocab) < vocab_size:\n", "            pairs_freq = collections.defaultdict(int)\n", "            for segment_str, count in current_segments.items():\n", "                tokens = segment_str.split()\n", "                pairs = self.get_pairs(tokens)\n", "                for pair, freq in pairs.items():\n", "                    pairs_freq[pair] += freq * count\n", "\n", "            if not pairs_freq:\n", "                break\n", "\n", "            best_pair = max(pairs_freq.items(), key=lambda x: x[1])[0]\n", "            first, second = best_pair\n", "            new_token = first + second\n", "\n", "            self.merges[best_pair] = new_token\n", "\n", "            # Update segments\n", "            updated_segments = collections.defaultdict(int)\n", "            for segment_str, count in current_segments.items():\n", "                tokens = segment_str.split()\n", "                i = 0\n", "                new_tokens = []\n", "                while i < len(tokens):\n", "                    if i < len(tokens) - 1 and tokens[i] == first and tokens[i+1] == second:\n", "                        new_tokens.append(new_token)\n", "                        i += 2\n", "                    else:\n", "                        new_tokens.append(tokens[i])\n", "                        i += 1\n", "                updated_segments[' '.join(new_tokens)] += count\n", "            current_segments = updated_segments\n", "\n", "            if new_token not in self.vocab:\n", "                self.vocab[new_token] = current_id\n", "                self.inverse_vocab[current_id] = new_token\n", "                current_id += 1\n", "\n", "        print(f\"Trained tokenizer with {len(self.vocab)} tokens\")\n", "\n", "    def encode(self, text):\n", "        words = text.split()\n", "        encoded = []\n", "        \n", "        for word in words:\n", "            if not word:\n", "                continue\n", "            \n", "            tokens = list(word) + [self.eos_token]\n", "            \n", "            while True:\n", "                pairs = self.get_pairs(tokens)\n", "                potential_merges = [(p, self.merges[p]) for p in pairs.keys() if p in self.merges]\n", "                \n", "                if not potential_merges:\n", "                    break\n", "                \n", "                selected_pair, new_token = max(potential_merges, key=lambda x: len(x[1]))\n", "                first, second = selected_pair\n", "                \n", "                i = 0\n", "                new_tokens = []\n", "                while i < len(tokens):\n", "                    if i < len(tokens) - 1 and tokens[i] == first and tokens[i+1] == second:\n", "                        new_tokens.append(new_token)\n", "                        i += 2\n", "                    else:\n", "                        new_tokens.append(tokens[i])\n", "                        i += 1\n", "                tokens = new_tokens\n", "            \n", "            for token in tokens:\n", "                encoded.append(self.vocab.get(token, self.vocab[self.unk_token]))\n", "        \n", "        return encoded\n", "\n", "    def decode(self, token_ids):\n", "        tokens = [self.inverse_vocab.get(tid, '') for tid in token_ids]\n", "        text = ''.join(tokens).replace(self.eos_token, ' ').strip()\n", "        return ' '.join(text.split())\n", "\n", "print(\"Tokenizer ready!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "model"}, "outputs": [], "source": ["# Transformer Model Components\n", "class MultiHeadAttention(nn.Module):\n", "    def __init__(self, embed_dim, num_heads, dropout=0.1):\n", "        super().__init__()\n", "        self.embed_dim = embed_dim\n", "        self.num_heads = num_heads\n", "        self.head_dim = embed_dim // num_heads\n", "        \n", "        self.q_linear = nn.Linear(embed_dim, embed_dim)\n", "        self.k_linear = nn.Linear(embed_dim, embed_dim)\n", "        self.v_linear = nn.Linear(embed_dim, embed_dim)\n", "        self.out_linear = nn.Linear(embed_dim, embed_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "        \n", "    def forward(self, query, key, value, mask=None):\n", "        batch_size, seq_len = query.size(0), query.size(1)\n", "        \n", "        Q = self.q_linear(query).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)\n", "        K = self.k_linear(key).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)\n", "        V = self.v_linear(value).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)\n", "        \n", "        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.head_dim)\n", "        \n", "        if mask is not None:\n", "            scores = scores.masked_fill(mask == 0, -1e9)\n", "        \n", "        attn_weights = F.softmax(scores, dim=-1)\n", "        attn_weights = self.dropout(attn_weights)\n", "        \n", "        context = torch.matmul(attn_weights, V)\n", "        context = context.transpose(1, 2).contiguous().view(batch_size, seq_len, self.embed_dim)\n", "        \n", "        return self.out_linear(context)\n", "\n", "class FeedForward(nn.Module):\n", "    def __init__(self, embed_dim, ff_dim, dropout=0.1):\n", "        super().__init__()\n", "        self.linear1 = nn.Linear(embed_dim, ff_dim)\n", "        self.linear2 = nn.Linear(ff_dim, embed_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "        \n", "    def forward(self, x):\n", "        return self.linear2(self.dropout(<PERSON><PERSON>relu(self.linear1(x))))\n", "\n", "class TransformerBlock(nn.Module):\n", "    def __init__(self, embed_dim, num_heads, ff_dim, dropout=0.1):\n", "        super().__init__()\n", "        self.attention = MultiHeadAttention(embed_dim, num_heads, dropout)\n", "        self.feed_forward = FeedForward(embed_dim, ff_dim, dropout)\n", "        self.norm1 = nn.LayerNorm(embed_dim)\n", "        self.norm2 = nn.LayerNorm(embed_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "        \n", "    def forward(self, x, mask=None):\n", "        attn_out = self.attention(x, x, x, mask)\n", "        x = self.norm1(x + self.dropout(attn_out))\n", "        ff_out = self.feed_forward(x)\n", "        x = self.norm2(x + self.dropout(ff_out))\n", "        return x\n", "\n", "class LanguageModel(nn.Module):\n", "    def __init__(self, vocab_size, embed_dim, num_layers, num_heads, ff_dim, max_seq_len, dropout=0.1):\n", "        super().__init__()\n", "        self.embed_dim = embed_dim\n", "        self.token_embedding = nn.Embedding(vocab_size, embed_dim)\n", "        self.pos_embedding = nn.Embedding(max_seq_len, embed_dim)\n", "        \n", "        self.blocks = nn.ModuleList([\n", "            TransformerBlock(embed_dim, num_heads, ff_dim, dropout)\n", "            for _ in range(num_layers)\n", "        ])\n", "        \n", "        self.ln_f = nn.LayerNorm(embed_dim)\n", "        self.head = nn.Linear(embed_dim, vocab_size, bias=False)\n", "        self.dropout = nn.Dropout(dropout)\n", "        \n", "    def forward(self, x, targets=None):\n", "        batch_size, seq_len = x.size()\n", "        \n", "        # Create causal mask\n", "        mask = torch.tril(torch.ones(seq_len, seq_len)).unsqueeze(0).unsqueeze(0).to(x.device)\n", "        \n", "        # Embeddings\n", "        pos = torch.arange(0, seq_len, dtype=torch.long, device=x.device).unsqueeze(0)\n", "        tok_emb = self.token_embedding(x)\n", "        pos_emb = self.pos_embedding(pos)\n", "        x = self.dropout(tok_emb + pos_emb)\n", "        \n", "        # Transformer blocks\n", "        for block in self.blocks:\n", "            x = block(x, mask)\n", "        \n", "        x = self.ln_f(x)\n", "        logits = self.head(x)\n", "        \n", "        loss = None\n", "        if targets is not None:\n", "            loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=-1)\n", "        \n", "        return logits, loss\n", "\n", "print(\"Model architecture ready!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "data"}, "outputs": [], "source": ["# Data Loading and Preprocessing\n", "print(\"Loading dataset...\")\n", "dataset = load_dataset(\"wikitext\", \"wikitext-103-raw-v1\", split=\"train[:50000]\")\n", "texts = [entry[\"text\"].strip() for entry in dataset if entry[\"text\"].strip()]\n", "print(f\"Loaded {len(texts)} text samples\")\n", "\n", "# Train tokenizer\n", "print(\"Training tokenizer...\")\n", "tokenizer = BPETokenizer()\n", "tokenizer.train(texts[:10000], vocab_size=2000)  # Use subset for tokenizer training\n", "\n", "# Tokenize all texts\n", "print(\"Tokenizing texts...\")\n", "tokenized_texts = []\n", "for i, text in enumerate(texts):\n", "    if i % 1000 == 0:\n", "        print(f\"Tokenized {i}/{len(texts)} texts\", end='\\r')\n", "    tokens = tokenizer.encode(text)\n", "    if len(tokens) > 5:  # Filter very short texts\n", "        tokenized_texts.append(tokens)\n", "\n", "print(f\"\\nTokenized {len(tokenized_texts)} texts\")\n", "print(f\"Vocabulary size: {len(tokenizer.vocab)}\")\n", "\n", "# Dataset class\n", "class TextDataset(Dataset):\n", "    def __init__(self, tokenized_texts, max_length=128):\n", "        self.data = []\n", "        for tokens in tokenized_texts:\n", "            # Create sliding windows\n", "            for i in range(0, len(tokens) - max_length, max_length // 2):\n", "                chunk = tokens[i:i + max_length]\n", "                if len(chunk) == max_length:\n", "                    self.data.append(chunk)\n", "    \n", "    def __len__(self):\n", "        return len(self.data)\n", "    \n", "    def __getitem__(self, idx):\n", "        tokens = self.data[idx]\n", "        x = torch.tensor(tokens[:-1], dtype=torch.long)\n", "        y = torch.tensor(tokens[1:], dtype=torch.long)\n", "        return x, y\n", "\n", "# Create dataset and dataloader\n", "dataset = TextDataset(tokenized_texts, max_length=64)\n", "dataloader = DataLoader(dataset, batch_size=32, shuffle=True, num_workers=2)\n", "\n", "print(f\"Dataset ready with {len(dataset)} samples\")\n", "print(f\"Batches per epoch: {len(dataloader)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "training"}, "outputs": [], "source": ["# Model Configuration and Training Setup\n", "model_config = {\n", "    'vocab_size': len(tokenizer.vocab),\n", "    'embed_dim': 384,\n", "    'num_layers': 6,\n", "    'num_heads': 6,\n", "    'ff_dim': 1536,\n", "    'max_seq_len': 64,\n", "    'dropout': 0.1\n", "}\n", "\n", "print(f\"Model config: {model_config}\")\n", "\n", "# Initialize model\n", "model = LanguageModel(**model_config).to(device)\n", "optimizer = torch.optim.AdamW(model.parameters(), lr=3e-4, weight_decay=0.1)\n", "scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=1000)\n", "\n", "# Count parameters\n", "total_params = sum(p.numel() for p in model.parameters())\n", "print(f\"Model has {total_params:,} parameters\")\n", "\n", "# Training function\n", "def train_epoch(model, dataloader, optimizer, scheduler, device):\n", "    model.train()\n", "    total_loss = 0\n", "    total_tokens = 0\n", "    \n", "    for batch_idx, (x, y) in enumerate(dataloader):\n", "        x, y = x.to(device), y.to(device)\n", "        \n", "        optimizer.zero_grad()\n", "        \n", "        with torch.cuda.amp.autocast() if device.type == 'cuda' else torch.no_grad():\n", "            logits, loss = model(x, y)\n", "        \n", "        if device.type == 'cuda':\n", "            scaler.scale(loss).backward()\n", "            scaler.step(optimizer)\n", "            scaler.update()\n", "        else:\n", "            loss.backward()\n", "            optimizer.step()\n", "        \n", "        scheduler.step()\n", "        \n", "        total_loss += loss.item()\n", "        total_tokens += y.numel()\n", "        \n", "        if batch_idx % 100 == 0:\n", "            current_loss = total_loss / (batch_idx + 1)\n", "            perplexity = math.exp(current_loss)\n", "            print(f\"Batch {batch_idx:4d}/{len(dataloader)} | Loss: {current_loss:.4f} | PPL: {perplexity:.2f} | LR: {scheduler.get_last_lr()[0]:.6f}\")\n", "    \n", "    avg_loss = total_loss / len(dataloader)\n", "    perplexity = math.exp(avg_loss)\n", "    return avg_loss, perplexity\n", "\n", "# Initialize scaler for mixed precision\n", "if device.type == 'cuda':\n", "    scaler = torch.cuda.amp.GradScaler()\n", "\n", "print(\"Training setup complete!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "train"}, "outputs": [], "source": ["# Training Loop\n", "num_epochs = 15\n", "best_loss = float('inf')\n", "\n", "print(f\"Starting training for {num_epochs} epochs...\")\n", "print(\"=\" * 60)\n", "\n", "for epoch in range(num_epochs):\n", "    start_time = time.time()\n", "    \n", "    avg_loss, perplexity = train_epoch(model, dataloader, optimizer, scheduler, device)\n", "    \n", "    epoch_time = time.time() - start_time\n", "    \n", "    print(f\"\\nEpoch {epoch+1:2d}/{num_epochs} | Loss: {avg_loss:.4f} | PPL: {perplexity:.2f} | Time: {epoch_time:.1f}s\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Save best model\n", "    if avg_loss < best_loss:\n", "        best_loss = avg_loss\n", "        torch.save({\n", "            'model_state_dict': model.state_dict(),\n", "            'optimizer_state_dict': optimizer.state_dict(),\n", "            'epoch': epoch,\n", "            'loss': avg_loss,\n", "            'config': model_config\n", "        }, 'best_model.pt')\n", "        print(f\"💾 Saved best model (loss: {avg_loss:.4f})\")\n", "\n", "print(\"\\n🎉 Training completed!\")\n", "print(f\"Best loss: {best_loss:.4f} | Best perplexity: {math.exp(best_loss):.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "generate"}, "outputs": [], "source": ["# Text Generation Function\n", "def generate_text(model, tokenizer, prompt, max_length=50, temperature=1.0, top_k=50):\n", "    model.eval()\n", "    \n", "    # Encode prompt\n", "    tokens = tokenizer.encode(prompt)\n", "    input_ids = torch.tensor([tokens], dtype=torch.long).to(device)\n", "    \n", "    generated = tokens.copy()\n", "    \n", "    with torch.no_grad():\n", "        for _ in range(max_length):\n", "            # Get model predictions\n", "            logits, _ = model(input_ids)\n", "            \n", "            # Get logits for last token and apply temperature\n", "            next_token_logits = logits[0, -1, :] / temperature\n", "            \n", "            # Apply top-k filtering\n", "            if top_k > 0:\n", "                indices_to_remove = next_token_logits < torch.topk(next_token_logits, top_k)[0][..., -1, None]\n", "                next_token_logits[indices_to_remove] = -float('inf')\n", "            \n", "            # Sample next token\n", "            probs = F.softmax(next_token_logits, dim=-1)\n", "            next_token = torch.multinomial(probs, 1).item()\n", "            \n", "            # Add to sequence\n", "            generated.append(next_token)\n", "            input_ids = torch.cat([input_ids, torch.tensor([[next_token]], device=device)], dim=1)\n", "            \n", "            # Keep only recent context to avoid memory issues\n", "            if input_ids.size(1) > 63:\n", "                input_ids = input_ids[:, -63:]\n", "    \n", "    return tokenizer.decode(generated)\n", "\n", "# Load best model for generation\n", "checkpoint = torch.load('best_model.pt', map_location=device)\n", "model.load_state_dict(checkpoint['model_state_dict'])\n", "print(f\"Loaded best model from epoch {checkpoint['epoch']+1}\")\n", "\n", "# Test generation\n", "test_prompts = [\n", "    \"The quick brown fox\",\n", "    \"Once upon a time\",\n", "    \"In the beginning\",\n", "    \"The weather today is\",\n", "    \"I think that\",\n", "    \"The future of artificial intelligence\"\n", "]\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"🤖 TESTING MODEL FLUENCY\")\n", "print(\"=\" * 60)\n", "\n", "for prompt in test_prompts:\n", "    print(f\"\\n📝 Prompt: '{prompt}'\")\n", "    print(\"-\" * 40)\n", "    \n", "    for temp in [0.7, 1.0, 1.3]:\n", "        generated = generate_text(model, tokenizer, prompt, max_length=30, temperature=temp)\n", "        print(f\"🌡️  Temp {temp}: {generated}\")\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"✅ Generation test complete!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "interactive"}, "outputs": [], "source": ["# Interactive Generation\n", "print(\"🎮 INTERACTIVE MODE\")\n", "print(\"Enter prompts to generate text (type 'quit' to exit)\")\n", "print(\"=\" * 50)\n", "\n", "while True:\n", "    try:\n", "        user_prompt = input(\"\\n💭 Your prompt: \").strip()\n", "        \n", "        if user_prompt.lower() in ['quit', 'exit', 'q', 'stop']:\n", "            print(\"👋 Goodbye!\")\n", "            break\n", "            \n", "        if user_prompt:\n", "            generated = generate_text(model, tokenizer, user_prompt, max_length=40, temperature=1.0)\n", "            print(f\"🤖 Generated: {generated}\")\n", "        \n", "    except KeyboardInterrupt:\n", "        print(\"\\n👋 Goodbye!\")\n", "        break\n", "    except Exception as e:\n", "        print(f\"❌ Error: {e}\")"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}