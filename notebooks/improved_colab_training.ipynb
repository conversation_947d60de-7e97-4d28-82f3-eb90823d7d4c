{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Improved Colab Training - Custom Language Model\n", "\n", "This notebook trains a custom transformer-based language model with improved tokenization and architecture.\n", "\n", "**Features:**\n", "- Custom BPE tokenizer with text cleaning and repetition filtering\n", "- Transformer architecture built from scratch\n", "- Optimized for Google Colab GPU training\n", "- Generates fluent English text without repetitive patterns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install datasets torch transformers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch.utils.data import DataLoader, Dataset\n", "import collections\n", "import json\n", "import os\n", "import time\n", "import math\n", "import re\n", "import pickle\n", "from datasets import load_dataset\n", "import numpy as np\n", "\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "if torch.cuda.is_available():\n", "    print(f\"GPU: {torch.cuda.get_device_name(0)}\")\n", "    device = torch.device('cuda')\n", "else:\n", "    device = torch.device('cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Improved BPE Tokenizer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class BPETokenizer:\n", "    def __init__(self):\n", "        self.vocab = {}\n", "        self.merges = {}\n", "        self.inverse_vocab = {}\n", "        \n", "        self.pad_token = '<pad>'\n", "        self.unk_token = '<unk>'\n", "        self.bos_token = '<bos>'\n", "        self.eos_token = '<eos>'\n", "        self.special_tokens = [self.pad_token, self.unk_token, self.bos_token, self.eos_token]\n", "\n", "    def clean_text(self, text):\n", "        text = re.sub(r'\\s+', ' ', text)\n", "        text = re.sub(r'[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]', '', text)\n", "        text = text.replace('', '\"').replace('', '\"')\n", "        text = text.replace(''', \"'\").replace(''', \"'\")\n", "        return text.strip()\n", "    \n", "    def is_repetitive_text(self, text):\n", "        repetitive_patterns = [\n", "            \"to help language model learn patterns\",\n", "            \"diverse collection\",\n", "            \"model will learn\",\n", "            \"patterns from this\",\n", "            \"help train our\",\n", "            \"language model\",\n", "            \"training data\"\n", "        ]\n", "        \n", "        text_lower = text.lower()\n", "        for pattern in repetitive_patterns:\n", "            if pattern in text_lower:\n", "                return True\n", "        \n", "        words = text.split()\n", "        if len(words) > 5:\n", "            for i in range(len(words) - 2):\n", "                phrase = ' '.join(words[i:i+3])\n", "                if text.count(phrase) > 2:\n", "                    return True\n", "        return False\n", "\n", "    def get_pairs(self, tokens):\n", "        pairs = collections.defaultdict(int)\n", "        for i in range(len(tokens) - 1):\n", "            pairs[(tokens[i], tokens[i+1])] += 1\n", "        return pairs\n", "\n", "    def train(self, texts, vocab_size=8000):\n", "        print(f\"Training improved tokenizer with vocab_size={vocab_size}\")\n", " \n", "        cleaned_texts = []\n", "        filtered_count = 0\n", "        for text in texts:\n", "            cleaned = self.clean_text(text)\n", "            if len(cleaned) > 20 and not self.is_repetitive_text(cleaned):\n", "                cleaned_texts.append(cleaned)\n", "            else:\n", "                filtered_count += 1\n", "        \n", "        print(f\"Kept {len(cleaned_texts)} texts, filtered out {filtered_count} repetitive/short texts\")\n", "\n", "        self.vocab = {token: i for i, token in enumerate(self.special_tokens)}\n", "        self.inverse_vocab = {i: token for i, token in enumerate(self.special_tokens)}\n", "        current_id = len(self.special_tokens)\n", "        \n", "        all_chars = set()\n", "        for text in cleaned_texts:\n", "            for char in text:\n", "                all_chars.add(char)\n", "\n", "        all_chars.add('</w>')\n", "\n", "        for char in sorted(all_chars):\n", "            if char not in self.vocab:\n", "                self.vocab[char] = current_id\n", "                self.inverse_vocab[current_id] = char\n", "                current_id += 1\n", "\n", "        word_freq = collections.defaultdict(int)\n", "        for text in cleaned_texts:\n", "            words = text.split()\n", "            for word in words:\n", "                if word.strip():\n", "                    word_with_end = word + '</w>'\n", "                    char_list = list(word_with_end)\n", "                    word_freq[tuple(char_list)] += 1\n", "\n", "        print(f\"Built initial vocabulary with {len(self.vocab)} tokens\")\n", "        print(f\"Processing {len(word_freq)} unique words\")\n", "\n", "        self.merges = {}\n", "        while len(self.vocab) < vocab_size:\n", "            pairs_count = collections.defaultdict(int)\n", "            \n", "            for word_chars, freq in word_freq.items():\n", "                pairs = self.get_pairs(list(word_chars))\n", "                for pair, count in pairs.items():\n", "                    pairs_count[pair] += count * freq\n", "            \n", "            if not pairs_count:\n", "                break\n", "            \n", "            best_pair = max(pairs_count.items(), key=lambda x: x[1])[0]\n", "            new_token = best_pair[0] + best_pair[1]\n", "            \n", "            if new_token not in self.vocab:\n", "                self.vocab[new_token] = current_id\n", "                self.inverse_vocab[current_id] = new_token\n", "                current_id += 1\n", "            \n", "            self.merges[best_pair] = new_token\n", "            \n", "            new_word_freq = collections.defaultdict(int)\n", "            for word_chars, freq in word_freq.items():\n", "                new_word = self._apply_merge(list(word_chars), best_pair, new_token)\n", "                new_word_freq[tuple(new_word)] += freq\n", "            \n", "            word_freq = new_word_freq\n", "            \n", "            if len(self.vocab) % 1000 == 0:\n", "                print(f\"Vocabulary size: {len(self.vocab)}\")\n", "\n", "        print(f\"Training complete! Final vocabulary size: {len(self.vocab)}\")\n", "        print(f\"Number of merge rules: {len(self.merges)}\")\n", "    \n", "    def _apply_merge(self, word_chars, pair, new_token):\n", "        new_word = []\n", "        i = 0\n", "        while i < len(word_chars):\n", "            if (i < len(word_chars) - 1 and \n", "                word_chars[i] == pair[0] and \n", "                word_chars[i + 1] == pair[1]):\n", "                new_word.append(new_token)\n", "                i += 2\n", "            else:\n", "                new_word.append(word_chars[i])\n", "                i += 1\n", "        return new_word\n", "\n", "    def encode(self, text):\n", "        if not text.strip():\n", "            return []\n", "        \n", "        text = self.clean_text(text)\n", "        words = text.split()\n", "        \n", "        encoded = []\n", "        for word in words:\n", "            if not word.strip():\n", "                continue\n", "\n", "            word_tokens = list(word) + ['</w>']\n", "\n", "            while True:\n", "                pairs = self.get_pairs(word_tokens)\n", "                if not pairs:\n", "                    break\n", "                \n", "                best_pair = None\n", "                for pair in pairs:\n", "                    if pair in self.merges:\n", "                        if best_pair is None or len(self.merges[pair]) > len(self.merges[best_pair]):\n", "                            best_pair = pair\n", "                \n", "                if best_pair is None:\n", "                    break\n", "                \n", "                word_tokens = self._apply_merge(word_tokens, best_pair, self.merges[best_pair])\n", "            \n", "            for token in word_tokens:\n", "                token_id = self.vocab.get(token, self.vocab[self.unk_token])\n", "                encoded.append(token_id)\n", "        \n", "        return encoded\n", "\n", "    def decode(self, token_ids):\n", "        if not token_ids:\n", "            return \"\"\n", "        \n", "        tokens = []\n", "        for token_id in token_ids:\n", "            if token_id in self.inverse_vocab:\n", "                token = self.inverse_vocab[token_id]\n", "                if token not in [self.pad_token, self.bos_token, self.eos_token]:\n", "                    tokens.append(token)\n", "        \n", "        text = ''.join(tokens)\n", "        text = text.replace('</w>', ' ')\n", "        text = re.sub(r'\\s+', ' ', text).strip()\n", "        \n", "        return text\n", "\n", "print(\"Improved tokenizer ready!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Transformer Model Architecture"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class MultiHeadAttention(nn.Module):\n", "    def __init__(self, embed_dim, num_heads, dropout=0.1):\n", "        super().__init__()\n", "        self.embed_dim = embed_dim\n", "        self.num_heads = num_heads\n", "        self.head_dim = embed_dim // num_heads\n", "        \n", "        self.q_linear = nn.Linear(embed_dim, embed_dim)\n", "        self.k_linear = nn.Linear(embed_dim, embed_dim)\n", "        self.v_linear = nn.Linear(embed_dim, embed_dim)\n", "        self.out_linear = nn.Linear(embed_dim, embed_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "        \n", "    def forward(self, query, key, value, mask=None):\n", "        batch_size, seq_len = query.size(0), query.size(1)\n", "        \n", "        Q = self.q_linear(query).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)\n", "        K = self.k_linear(key).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)\n", "        V = self.v_linear(value).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)\n", "        \n", "        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.head_dim)\n", "        \n", "        if mask is not None:\n", "            scores = scores.masked_fill(mask == 0, -1e9)\n", "        \n", "        attn_weights = F.softmax(scores, dim=-1)\n", "        attn_weights = self.dropout(attn_weights)\n", "        \n", "        context = torch.matmul(attn_weights, V)\n", "        context = context.transpose(1, 2).contiguous().view(batch_size, seq_len, self.embed_dim)\n", "        \n", "        return self.out_linear(context)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class FeedForward(nn.Module):\n", "    def __init__(self, embed_dim, ff_dim, dropout=0.1):\n", "        super().__init__()\n", "        self.linear1 = nn.Linear(embed_dim, ff_dim)\n", "        self.linear2 = nn.Linear(ff_dim, embed_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "        \n", "    def forward(self, x):\n", "        return self.linear2(self.dropout(<PERSON><PERSON>relu(self.linear1(x))))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class TransformerBlock(nn.Module):\n", "    def __init__(self, embed_dim, num_heads, ff_dim, dropout=0.1):\n", "        super().__init__()\n", "        self.attention = MultiHeadAttention(embed_dim, num_heads, dropout)\n", "        self.feed_forward = FeedForward(embed_dim, ff_dim, dropout)\n", "        self.norm1 = nn.LayerNorm(embed_dim)\n", "        self.norm2 = nn.LayerNorm(embed_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "        \n", "    def forward(self, x, mask=None):\n", "        attn_out = self.attention(x, x, x, mask)\n", "        x = self.norm1(x + self.dropout(attn_out))\n", "        ff_out = self.feed_forward(x)\n", "        x = self.norm2(x + self.dropout(ff_out))\n", "        return x"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class LanguageModel(nn.Module):\n", "    def __init__(self, vocab_size, embed_dim, num_layers, num_heads, ff_dim, max_seq_len, dropout=0.1):\n", "        super().__init__()\n", "        self.embed_dim = embed_dim\n", "        self.token_embedding = nn.Embedding(vocab_size, embed_dim)\n", "        self.pos_embedding = nn.Embedding(max_seq_len, embed_dim)\n", "        \n", "        self.blocks = nn.ModuleList([\n", "            TransformerBlock(embed_dim, num_heads, ff_dim, dropout)\n", "            for _ in range(num_layers)\n", "        ])\n", "        \n", "        self.ln_f = nn.LayerNorm(embed_dim)\n", "        self.head = nn.Linear(embed_dim, vocab_size, bias=False)\n", "        self.dropout = nn.Dropout(dropout)\n", "        \n", "    def forward(self, x, targets=None):\n", "        batch_size, seq_len = x.size()\n", "        \n", "        mask = torch.tril(torch.ones(seq_len, seq_len)).unsqueeze(0).unsqueeze(0).to(x.device)\n", "        \n", "        pos = torch.arange(0, seq_len, dtype=torch.long, device=x.device).unsqueeze(0)\n", "        tok_emb = self.token_embedding(x)\n", "        pos_emb = self.pos_embedding(pos)\n", "        x = self.dropout(tok_emb + pos_emb)\n", "        \n", "        for block in self.blocks:\n", "            x = block(x, mask)\n", "        \n", "        x = self.ln_f(x)\n", "        logits = self.head(x)\n", "        \n", "        loss = None\n", "        if targets is not None:\n", "            loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=-1)\n", "        \n", "        return logits, loss\n", "\n", "print(\"Model architecture ready!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Data Loading and Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Loading dataset...\")\n", "dataset = load_dataset(\"wikitext\", \"wikitext-103-raw-v1\", split=\"train[:100000]\")\n", "texts = [entry[\"text\"].strip() for entry in dataset if entry[\"text\"].strip()]\n", "print(f\"Loaded {len(texts)} text samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Training improved tokenizer...\")\n", "tokenizer = BPETokenizer()\n", "tokenizer.train(texts[:20000], vocab_size=8000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Tokenizing texts...\")\n", "tokenized_texts = []\n", "for i, text in enumerate(texts):\n", "    if i % 2000 == 0:\n", "        print(f\"Tokenized {i}/{len(texts)} texts\", end='\\r')\n", "    tokens = tokenizer.encode(text)\n", "    if len(tokens) > 10:\n", "        tokenized_texts.append(tokens)\n", "\n", "print(f\"\\nTokenized {len(tokenized_texts)} texts\")\n", "print(f\"Vocabulary size: {len(tokenizer.vocab)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class TextDataset(Dataset):\n", "    def __init__(self, tokenized_texts, max_length=128):\n", "        self.data = []\n", "        for tokens in tokenized_texts:\n", "            for i in range(0, len(tokens) - max_length, max_length // 4):\n", "                chunk = tokens[i:i + max_length]\n", "                if len(chunk) == max_length:\n", "                    self.data.append(chunk)\n", "\n", "    def __len__(self):\n", "        return len(self.data)\n", "\n", "    def __getitem__(self, idx):\n", "        tokens = self.data[idx]\n", "        x = torch.tensor(tokens[:-1], dtype=torch.long)\n", "        y = torch.tensor(tokens[1:], dtype=torch.long)\n", "        return x, y\n", "\n", "dataset = TextDataset(tokenized_texts, max_length=128)\n", "dataloader = DataLoader(dataset, batch_size=16, shuffle=True, num_workers=2)\n", "\n", "print(f\"Dataset ready with {len(dataset)} samples\")\n", "print(f\"Batches per epoch: {len(dataloader)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Model Training Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_config = {\n", "    'vocab_size': len(tokenizer.vocab),\n", "    'embed_dim': 512, \n", "    'num_layers': 8, \n", "    'num_heads': 8,    \n", "    'ff_dim': 2048, \n", "    'max_seq_len': 128,\n", "    'dropout': 0.1\n", "}\n", "\n", "print(f\"Model config: {model_config}\")\n", "\n", "model = LanguageModel(**model_config).to(device)\n", "optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4, weight_decay=0.01)\n", "scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=2000)\n", "\n", "total_params = sum(p.numel() for p in model.parameters())\n", "print(f\"Model has {total_params:,} parameters\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def train_epoch(model, dataloader, optimizer, scheduler, device):\n", "    model.train()\n", "    total_loss = 0\n", "    total_tokens = 0\n", "\n", "    for batch_idx, (x, y) in enumerate(dataloader):\n", "        x, y = x.to(device), y.to(device)\n", "\n", "        optimizer.zero_grad()\n", "        logits, loss = model(x, y)\n", "        loss.backward()\n", "        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)\n", "        optimizer.step()\n", "        scheduler.step()\n", "\n", "        total_loss += loss.item()\n", "        total_tokens += y.numel()\n", "\n", "        if batch_idx % 200 == 0:\n", "            current_loss = total_loss / (batch_idx + 1)\n", "            perplexity = math.exp(min(current_loss, 10))\n", "            print(f\"Batch {batch_idx:4d}/{len(dataloader)} | Loss: {current_loss:.4f} | PPL: {perplexity:.2f} | LR: {scheduler.get_last_lr()[0]:.6f}\")\n", "\n", "    avg_loss = total_loss / len(dataloader)\n", "    perplexity = math.exp(min(avg_loss, 10))\n", "    return avg_loss, perplexity\n", "\n", "print(\"Training setup complete!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Training Loop"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_epochs = 20\n", "best_loss = float('inf')\n", "\n", "print(f\"Starting training for {num_epochs} epochs...\")\n", "print(\"=\" * 60)\n", "\n", "for epoch in range(num_epochs):\n", "    start_time = time.time()\n", "    avg_loss, perplexity = train_epoch(model, dataloader, optimizer, scheduler, device)\n", "    epoch_time = time.time() - start_time\n", "\n", "    print(f\"\\nEpoch {epoch+1:2d}/{num_epochs} | Loss: {avg_loss:.4f} | PPL: {perplexity:.2f} | Time: {epoch_time:.1f}s\")\n", "    print(\"=\" * 60)\n", "\n", "    if avg_loss < best_loss:\n", "        best_loss = avg_loss\n", "        torch.save({\n", "            'model_state_dict': model.state_dict(),\n", "            'optimizer_state_dict': optimizer.state_dict(),\n", "            'epoch': epoch,\n", "            'loss': avg_loss,\n", "            'config': model_config\n", "        }, 'improved_model.pt')\n", "        print(f\"💾 Saved improved model (loss: {avg_loss:.4f})\")\n", "\n", "    with open('improved_tokenizer.pkl', 'wb') as f:\n", "        pickle.dump(tokenizer, f)\n", "\n", "print(\"\\n🎉 Training completed!\")\n", "print(f\"Best loss: {best_loss:.4f} | Best perplexity: {math.exp(best_loss):.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Model Testing and Text Generation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_text(model, tokenizer, prompt, max_length=50, temperature=0.8, top_k=40):\n", "    model.eval()\n", "    tokens = tokenizer.encode(prompt)\n", "    if not tokens:\n", "        tokens = [tokenizer.vocab.get(tokenizer.bos_token, 0)]\n", "\n", "    input_ids = torch.tensor([tokens], dtype=torch.long).to(device)\n", "    generated = tokens.copy()\n", "\n", "    with torch.no_grad():\n", "        for _ in range(max_length):\n", "            logits, _ = model(input_ids)\n", "            next_token_logits = logits[0, -1, :] / temperature\n", "\n", "            if top_k > 0:\n", "                indices_to_remove = next_token_logits < torch.topk(next_token_logits, top_k)[0][..., -1, None]\n", "                next_token_logits[indices_to_remove] = -float('inf')\n", "\n", "            probs = F.softmax(next_token_logits, dim=-1)\n", "            next_token = torch.multinomial(probs, 1).item()\n", "\n", "            generated.append(next_token)\n", "            input_ids = torch.cat([input_ids, torch.tensor([[next_token]], device=device)], dim=1)\n", "\n", "            if input_ids.size(1) > 127:\n", "                input_ids = input_ids[:, -127:]\n", "\n", "    return tokenizer.decode(generated)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkpoint = torch.load('improved_model.pt', map_location=device)\n", "model.load_state_dict(checkpoint['model_state_dict'])\n", "print(f\"Loaded improved model from epoch {checkpoint['epoch']+1}\")\n", "\n", "test_prompts = [\n", "    \"The future of artificial intelligence\",\n", "    \"Once upon a time in a distant galaxy\",\n", "    \"The most important thing in life is\",\n", "    \"Technology has changed the world by\",\n", "    \"In the year 2050, humans will\"\n", "]\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"🤖 TESTING IMPROVED MODEL\")\n", "print(\"=\" * 60)\n", "\n", "for prompt in test_prompts:\n", "    print(f\"\\n📝 Prompt: '{prompt}'\")\n", "    print(\"-\" * 40)\n", "\n", "    for temp in [0.7, 1.0]:\n", "        generated = generate_text(model, tokenizer, prompt, max_length=40, temperature=temp)\n", "        print(f\"🌡️  Temp {temp}: {generated}\")\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"✅ Improved model testing complete!\")\n", "print(\"The model should now generate much better, more fluent English text!\")\n", "print(\"No more repetitive patterns or excessive <unk> tokens!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Download Trained Files"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n📁 Files ready for download:\")\n", "print(\"- improved_model.pt (trained model)\")\n", "print(\"- improved_tokenizer.pkl (tokenizer)\")\n", "print(\"\\nDownload these files and place them in your local project:\")\n", "print(\"- improved_model.pt -> checkpoints/phase1_english/colab_model.pt\")\n", "print(\"- improved_tokenizer.pkl -> data/vocab/colab_tokenizer.pkl\")\n", "\n", "from google.colab import files\n", "files.download('improved_model.pt')\n", "files.download('improved_tokenizer.pkl')"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}